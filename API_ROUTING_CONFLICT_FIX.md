# 🔧 API路由冲突修复指南

## ✅ 问题诊断

### 🔍 根本问题
从终端日志可以看到前端仍然在使用**两套API系统**：

1. **本地Hono API** (错误的)
   - 路径: `/api/projects`
   - 使用mock数据库
   - 日志: `Using mock database for local development`

2. **FastAPI后端** (正确的)
   - 路径: `/api/v1/canva/projects/`
   - 使用真实数据库
   - 我们配置的目标系统

### 📊 冲突证据
```
DATABASE_URL: postgresql://test:test@localhost:5432/test
Using mock database for local development
🔄 Projects API loaded
GET /api/projects - 使用本地mock API ❌
POST /api/v1/canva/projects/ - 应该使用FastAPI ✅
```

## 🔧 修复方案

### 方案1: 禁用本地API路由 (推荐)

#### 1.1 重命名本地API文件
```bash
# 临时禁用本地API路由
mv src/app/api/[[...route]]/route.ts src/app/api/[[...route]]/route.ts.disabled
mv src/app/api/[[...route]]/projects.ts src/app/api/[[...route]]/projects.ts.disabled
```

#### 1.2 或者修改路由条件
```typescript
// 在 src/app/api/[[...route]]/route.ts 中添加条件
const USE_FASTAPI_BACKEND = process.env.NEXT_PUBLIC_USE_FASTAPI === 'true';

if (USE_FASTAPI_BACKEND) {
  // 返回404，强制使用FastAPI后端
  return new Response('Use FastAPI backend', { status: 404 });
}
```

### 方案2: 环境变量控制

#### 2.1 添加环境变量
```env
# .env.local
NEXT_PUBLIC_USE_FASTAPI=true
NEXT_PUBLIC_API_URL=http://localhost:8000
```

#### 2.2 修改API客户端选择逻辑
```typescript
// 在组件中根据环境变量选择API
const USE_FASTAPI = process.env.NEXT_PUBLIC_USE_FASTAPI === 'true';

export const useCreateProject = () => {
  if (USE_FASTAPI) {
    // 使用FastAPI后端
    return useFastAPICreateProject();
  } else {
    // 使用本地mock API
    return useLocalCreateProject();
  }
};
```

### 方案3: 完全移除本地API (最彻底)

#### 3.1 删除本地API文件
```bash
rm -rf src/app/api/[[...route]]/
rm src/lib/hono.ts
```

#### 3.2 更新所有引用
- 移除所有对`@/lib/hono`的引用
- 确保所有Hook使用`projectService`

## 🧪 测试步骤

### 1. 实施修复
选择上述方案之一并实施。

### 2. 清除缓存
```javascript
// 浏览器控制台
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### 3. 重新登录
1. 访问: http://localhost:3000/sign-in
2. 登录: `demo` / `demo123`
3. 观察网络面板

### 4. 测试项目创建
1. 点击 "Start creating"
2. 检查网络面板中的请求
3. 应该只看到对FastAPI的调用

#### 预期网络请求
```
✅ POST http://localhost:8000/api/v1/canva/projects/
❌ 不应该有 POST /api/projects
```

## 🔍 调试检查清单

### ✅ 网络面板检查
- [ ] 没有对`/api/projects`的调用
- [ ] 只有对`/api/v1/canva/projects/`的调用
- [ ] 请求包含正确的认证头
- [ ] 返回200状态码

### ✅ 控制台日志检查
- [ ] 没有mock数据库日志
- [ ] 看到FastAPI相关日志
- [ ] 认证状态检查通过
- [ ] 项目创建成功

### ✅ 后端日志检查
- [ ] 收到项目创建请求
- [ ] JWT认证通过
- [ ] 项目成功创建并返回

## 🎯 快速修复 (临时方案)

如果需要立即测试，可以使用以下快速修复：

### 1. 临时禁用本地API
```typescript
// 在 src/app/api/[[...route]]/route.ts 开头添加
if (process.env.NEXT_PUBLIC_USE_FASTAPI === 'true') {
  return new Response('Disabled - using FastAPI', { status: 404 });
}
```

### 2. 设置环境变量
```bash
# .env.local
NEXT_PUBLIC_USE_FASTAPI=true
```

### 3. 重启前端服务
```bash
npm run dev
```

## 🚀 预期结果

修复后应该看到：

### 成功的项目创建流程
```
🔍 Checking auth status before creating project...
✅ User is authenticated
✅ Auth status OK, proceeding with project creation
🔑 Auth token check: { hasToken: true, ... }
🔐 Token header added
🔄 POST Request: http://localhost:8000/api/v1/canva/projects/
✅ API Response: { status: 200, ... }
🆕 Creating new project: Untitled project
✅ Project created successfully: 1
```

### 不应该再看到的日志
```
❌ Using mock database for local development
❌ 🔄 Projects API loaded
❌ GET /api/projects
❌ POST /api/projects
```

## 🎉 完整功能验证

修复后，你应该能够：

1. **正常登录**: JWT认证成功
2. **创建项目**: 调用FastAPI后端成功
3. **编辑项目**: 打开编辑器
4. **自动保存**: 画布数据保存到FastAPI后端
5. **项目列表**: 显示从FastAPI获取的项目

## 📋 后续步骤

1. **测试所有功能**: 确保项目CRUD操作正常
2. **移除冗余代码**: 清理不再使用的本地API代码
3. **更新文档**: 记录API架构变更
4. **部署准备**: 确保生产环境配置正确

让我们实施这个修复方案！🎨✨
