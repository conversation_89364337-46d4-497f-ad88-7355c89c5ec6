# 🔍 认证调试指南

## ✅ 已添加调试工具

我已经添加了详细的认证调试工具：

### 🛠️ 调试组件
- 在页面右下角显示实时认证状态
- 每5秒自动更新
- 显示token状态、过期时间等详细信息

### 📊 详细日志
- 项目创建前的完整认证检查
- localStorage中所有相关数据的详细信息
- token长度、预览、过期状态等

## 🧪 调试步骤

### 1. 查看调试面板
访问首页后，在页面右下角应该看到一个黑色的调试面板，显示：
- Access Token: ✅/❌
- Refresh Token: ✅/❌  
- Expires: 时间戳
- Is Expired: ✅/❌
- Auth Status: ✅/❌

### 2. 清除缓存并重新登录
```javascript
// 在浏览器控制台运行
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### 3. 重新登录并观察
1. 访问: http://localhost:3000/sign-in
2. 输入: `demo` / `demo123`
3. 观察控制台日志和调试面板

#### 预期登录后的调试面板
```
🔍 Auth Debug Info
Access Token: ✅ (200+ chars)
Refresh Token: ✅ (200+ chars)
Expires: 2025-08-13T...
Is Expired: ✅
Auth Status: ✅
Preview: eyJ0eXAiOiJKV1QiLCJhbGciOi...
```

### 4. 测试项目创建
1. 点击 "Start creating" 按钮
2. 观察控制台中的详细日志

#### 预期项目创建日志
```
🔍 Checking auth status before creating project...
🔍 Detailed auth check: {
  hasAccessToken: true,
  accessTokenLength: 200,
  accessTokenPreview: "eyJ0eXAiOiJKV1QiLCJhbGciOi...",
  hasRefreshToken: true,
  hasExpires: true,
  expires: "2025-08-13T...",
  isExpired: false,
  allKeys: ["canva_access_token", "canva_refresh_token", "canva_token_expires"]
}
✅ Auth status OK, proceeding with project creation
📊 Project data to create: { title: "Untitled project", ... }
```

## 🚨 可能的问题和解决方案

### 问题1: 调试面板显示❌
**症状**: Access Token显示❌，但用户已登录

**可能原因**:
1. Token存储键名不匹配
2. 登录流程没有正确存储token
3. NextAuth和我们的token系统冲突

**调试步骤**:
```javascript
// 在浏览器控制台检查
console.log('All localStorage:', Object.keys(localStorage));
console.log('Access token:', localStorage.getItem('canva_access_token'));
console.log('NextAuth session:', localStorage.getItem('nextauth.session-token'));
```

### 问题2: Token存在但认证失败
**症状**: 调试面板显示✅，但API调用返回422

**可能原因**:
1. Token格式错误
2. 后端JWT验证配置问题
3. 认证头格式不正确

**调试步骤**:
1. 检查网络面板中的请求头
2. 验证token格式（JWT应该有3个部分，用.分隔）
3. 检查后端日志中的JWT验证过程

### 问题3: Token过期
**症状**: 调试面板显示"Is Expired: ❌"

**解决方案**:
1. 重新登录获取新token
2. 实现自动刷新token机制

### 问题4: 存储键名不匹配
**症状**: 登录成功但找不到token

**检查步骤**:
```javascript
// 检查所有可能的token存储位置
console.log('canva_access_token:', localStorage.getItem('canva_access_token'));
console.log('access_token:', localStorage.getItem('access_token'));
console.log('token:', localStorage.getItem('token'));
console.log('authToken:', localStorage.getItem('authToken'));
```

## 🔧 临时修复方案

### 方案1: 手动设置token
如果登录成功但token没有正确存储：

```javascript
// 在登录成功后，手动检查和设置token
// 这可以帮助确定问题所在
const token = "你的JWT_TOKEN_这里";
localStorage.setItem('canva_access_token', token);
localStorage.setItem('canva_token_expires', (Date.now() + 24*60*60*1000).toString());
```

### 方案2: 强制重新认证
```javascript
// 清除所有认证相关数据并重新登录
localStorage.removeItem('canva_access_token');
localStorage.removeItem('canva_refresh_token');
localStorage.removeItem('canva_token_expires');
window.location.href = '/sign-in';
```

### 方案3: 检查NextAuth会话
```javascript
// 检查NextAuth是否有会话信息
import { getSession } from 'next-auth/react';

const session = await getSession();
console.log('NextAuth session:', session);
```

## 🎯 预期结果

正常工作时应该看到：

### 登录流程
```
🔐 Attempting login for: demo
✅ Login successful
🎫 Token received from FastAPI backend
🔍 Parsed JWT payload: { user_id: 2, username: 'demo', ... }
✅ FastAPI Login successful
```

### 调试面板
```
Access Token: ✅ (200+ chars)
Refresh Token: ✅ (200+ chars)
Expires: 2025-08-13T...
Is Expired: ✅
Auth Status: ✅
```

### 项目创建
```
🔍 Detailed auth check: { hasAccessToken: true, ... }
✅ Auth status OK, proceeding with project creation
🔄 POST Request: http://localhost:8000/api/v1/canva/projects/
✅ API Response: { status: 200, ... }
```

## 📋 下一步

1. **查看调试面板**: 确认token状态
2. **检查控制台日志**: 查看详细的认证信息
3. **测试项目创建**: 观察完整的调试流程
4. **报告结果**: 告诉我调试面板显示的具体信息

这样我们就能准确定位认证问题的根源！🔍✨
