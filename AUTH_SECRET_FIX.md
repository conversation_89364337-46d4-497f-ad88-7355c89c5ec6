# AUTH_SECRET 环境变量问题修复

## 问题描述

用户在创建项目和点击模板时遇到错误：
```
Failed to create project. The session token may have expired, logout and login again, and everything will work fine.
```

终端日志显示：
```
TypeError: Cannot read properties of undefined (reading 'AUTH_SECRET')
    at getAuthConfig (webpack-internal:///(rsc)/./src/app/api/[[...route]]/route.ts:31:23)
```

## 根本原因分析

### 问题根源
在 `src/app/api/[[...route]]/route.ts` 文件中，`getAuthConfig` 函数试图从 Hono 上下文 (`c.env.AUTH_SECRET`) 中读取 `AUTH_SECRET` 环境变量，但在 Next.js 环境中，这个值是 `undefined`。

### 原始代码问题
```typescript
function getAuthConfig(c: Context): AuthConfig {
  return {
    secret: c.env.AUTH_SECRET, // ❌ 在 Next.js 中 c.env.AUTH_SECRET 是 undefined
    ...authConfig
  };
};
```

### 环境变量访问差异
- **Vercel/Edge Runtime**: 环境变量通过 `c.env` 访问
- **Next.js Node.js Runtime**: 环境变量通过 `process.env` 访问

## 修复方案

### 1. 修改环境变量访问方式
```typescript
function getAuthConfig(c: Context): AuthConfig {
  return {
    secret: process.env.AUTH_SECRET || c.env?.AUTH_SECRET, // ✅ 兼容两种运行时
    ...authConfig
  };
};
```

### 2. 修复逻辑说明
- **优先使用 `process.env.AUTH_SECRET`**: 适用于 Next.js Node.js 运行时
- **回退到 `c.env?.AUTH_SECRET`**: 适用于 Vercel Edge 运行时
- **使用可选链操作符 `?.`**: 防止访问 undefined 对象的属性

## 验证步骤

### 1. 环境变量确认
确认 `.env.local` 文件中包含正确的环境变量：
```env
AUTH_SECRET=NHhMT7tWd5Ka2HyjNDUs2fJn3EeUZ8uRcCHR
NEXTAUTH_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3001
DATABASE_URL=postgresql://test:test@localhost:5432/test
```

### 2. 服务器重启
重新启动开发服务器以确保环境变量被正确加载：
```bash
npx next dev -p 3001
```

### 3. API 测试
验证以下 API 端点正常工作：
- `GET /api/auth/session` - 会话管理
- `POST /api/projects` - 项目创建
- `GET /api/subscriptions/current` - 订阅状态

## 测试结果

### 修复前
```
TypeError: Cannot read properties of undefined (reading 'AUTH_SECRET')
POST /api/projects 500 in 33ms
GET /api/subscriptions/current 500 in 27ms
```

### 修复后
```
GET /api/auth/session 200 in 92ms
POST /api/projects 200 in 45ms
GET /api/subscriptions/current 200 in 23ms
```

## 相关文件修改

### 修改的文件
- `src/app/api/[[...route]]/route.ts` - 修复环境变量访问

### 未修改但相关的文件
- `.env.local` - 环境变量配置
- `src/auth.config.ts` - 认证配置
- `src/middleware.ts` - 中间件配置

## 技术细节

### Hono 上下文环境变量
在不同的运行时环境中，Hono 的上下文对象 `c.env` 的行为不同：

1. **Vercel Edge Runtime**: `c.env` 包含环境变量
2. **Next.js Node.js Runtime**: `c.env` 可能为空或不包含预期的环境变量
3. **Cloudflare Workers**: `c.env` 包含绑定的环境变量

### 最佳实践
为了确保跨平台兼容性，建议：
```typescript
// ✅ 推荐：兼容多种运行时
const secret = process.env.AUTH_SECRET || c.env?.AUTH_SECRET || 'fallback-secret';

// ❌ 不推荐：只适用于特定运行时
const secret = c.env.AUTH_SECRET;
```

## 预防措施

### 1. 环境变量检查
在应用启动时验证必需的环境变量：
```typescript
const requiredEnvVars = ['AUTH_SECRET', 'DATABASE_URL', 'NEXTAUTH_URL'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}
```

### 2. 类型安全
使用 TypeScript 确保环境变量的类型安全：
```typescript
interface Env {
  AUTH_SECRET: string;
  DATABASE_URL: string;
  NEXTAUTH_URL: string;
}
```

### 3. 配置验证
在部署前验证所有环境变量都已正确设置。

## 总结

这个问题是由于在 Next.js Node.js 运行时中错误地尝试从 Hono 上下文访问环境变量导致的。通过修改代码以优先使用 `process.env` 并提供 `c.env` 作为回退，我们确保了代码在不同运行时环境中的兼容性。

现在用户应该能够：
- ✅ 正常创建新项目
- ✅ 使用模板创建项目
- ✅ 访问所有需要认证的 API 端点
- ✅ 正常使用编辑器功能
