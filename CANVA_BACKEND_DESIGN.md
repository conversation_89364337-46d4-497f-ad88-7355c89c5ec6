# Canva克隆项目后端架构设计方案

## 📋 项目概述

基于React+FastAPI管理系统的架构经验，为Canva克隆项目设计现代化的后端系统，实现用户管理、项目管理、草稿存储等核心功能。

## 🏗️ 技术栈选择

### 后端框架
- **FastAPI 0.116+** - 高性能异步Web框架
- **Tortoise ORM** - 异步数据库ORM
- **Granian** - ASGI服务器
- **Pydantic** - 数据验证和序列化
- **Redis** - 缓存和会话存储
- **PostgreSQL/SQLite** - 主数据库

### 认证与安全
- **JWT** - 无状态认证
- **bcrypt** - 密码加密
- **CORS** - 跨域资源共享
- **Rate Limiting** - 接口限流

### 文件存储
- **本地存储** - 开发环境
- **云存储集成** - 生产环境（阿里云OSS/AWS S3）
- **图片处理** - Pillow/ImageIO

## 📁 目录结构设计

```
canva-backend/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── core/                   # 核心功能
│   │   ├── __init__.py
│   │   ├── config.py          # 配置管理
│   │   ├── database.py        # 数据库连接
│   │   ├── security.py        # 安全相关
│   │   ├── exceptions.py      # 异常处理
│   │   └── middleware.py      # 中间件
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py            # 基础模型
│   │   ├── user.py            # 用户模型
│   │   ├── project.py         # 项目模型
│   │   ├── template.py        # 模板模型
│   │   └── file.py            # 文件模型
│   ├── schemas/                # Pydantic模式
│   │   ├── __init__.py
│   │   ├── user.py            # 用户模式
│   │   ├── project.py         # 项目模式
│   │   ├── template.py        # 模板模式
│   │   └── file.py            # 文件模式
│   ├── api/                    # API路由
│   │   ├── __init__.py
│   │   ├── v1/                # API版本1
│   │   │   ├── __init__.py
│   │   │   ├── auth.py        # 认证接口
│   │   │   ├── users.py       # 用户管理
│   │   │   ├── projects.py    # 项目管理
│   │   │   ├── templates.py   # 模板管理
│   │   │   └── files.py       # 文件管理
│   ├── services/               # 业务逻辑
│   │   ├── __init__.py
│   │   ├── auth_service.py    # 认证服务
│   │   ├── user_service.py    # 用户服务
│   │   ├── project_service.py # 项目服务
│   │   ├── template_service.py# 模板服务
│   │   └── file_service.py    # 文件服务
│   ├── utils/                  # 工具函数
│   │   ├── __init__.py
│   │   ├── password.py        # 密码工具
│   │   ├── jwt_utils.py       # JWT工具
│   │   ├── file_utils.py      # 文件工具
│   │   └── validators.py      # 验证器
│   └── tests/                  # 测试文件
│       ├── __init__.py
│       ├── test_auth.py
│       ├── test_users.py
│       ├── test_projects.py
│       └── test_files.py
├── migrations/                 # 数据库迁移
├── static/                     # 静态文件
├── uploads/                    # 上传文件
├── requirements.txt            # 依赖文件
├── pyproject.toml             # 项目配置
├── .env.example               # 环境变量示例
├── Dockerfile                 # Docker配置
└── README.md                  # 项目说明
```

## 🗄️ 数据库设计

### 核心数据表

#### 1. 用户表 (users)
```sql
- id: 主键
- username: 用户名 (唯一)
- email: 邮箱 (唯一)
- password_hash: 密码哈希
- avatar_url: 头像URL
- is_active: 是否激活
- is_premium: 是否高级用户
- created_at: 创建时间
- updated_at: 更新时间
- last_login: 最后登录时间
```

#### 2. 项目表 (projects)
```sql
- id: 主键
- user_id: 用户ID (外键)
- title: 项目标题
- description: 项目描述
- canvas_data: 画布数据 (JSON)
- thumbnail_url: 缩略图URL
- width: 画布宽度
- height: 画布高度
- is_template: 是否为模板
- is_public: 是否公开
- status: 项目状态 (draft/published/archived)
- created_at: 创建时间
- updated_at: 更新时间
```

#### 3. 项目版本表 (project_versions)
```sql
- id: 主键
- project_id: 项目ID (外键)
- version_number: 版本号
- canvas_data: 画布数据快照
- description: 版本描述
- created_at: 创建时间
```

#### 4. 文件表 (files)
```sql
- id: 主键
- user_id: 用户ID (外键)
- filename: 文件名
- original_name: 原始文件名
- file_path: 文件路径
- file_size: 文件大小
- mime_type: MIME类型
- file_type: 文件类型 (image/video/audio)
- is_public: 是否公开
- created_at: 创建时间
```

#### 5. 模板表 (templates)
```sql
- id: 主键
- creator_id: 创建者ID (外键)
- title: 模板标题
- description: 模板描述
- canvas_data: 画布数据
- thumbnail_url: 缩略图URL
- category: 模板分类
- tags: 标签 (JSON数组)
- is_featured: 是否精选
- download_count: 下载次数
- created_at: 创建时间
- updated_at: 更新时间
```

## 🔐 认证与授权系统

### JWT认证流程
1. 用户登录 → 验证凭据 → 生成JWT Token
2. 前端存储Token → 请求时携带Token
3. 后端验证Token → 解析用户信息 → 授权访问

### 权限控制
- **公开接口**: 注册、登录、公开模板查看
- **认证接口**: 项目CRUD、文件上传、用户信息
- **管理员接口**: 用户管理、模板审核、系统配置

## 📡 API接口设计

### 认证接口 (/api/v1/auth)
- POST /register - 用户注册
- POST /login - 用户登录
- POST /logout - 用户登出
- POST /refresh - 刷新Token
- GET /me - 获取当前用户信息

### 用户接口 (/api/v1/users)
- GET /profile - 获取用户资料
- PUT /profile - 更新用户资料
- POST /avatar - 上传头像
- GET /projects - 获取用户项目列表

### 项目接口 (/api/v1/projects)
- GET / - 获取项目列表
- POST / - 创建新项目
- GET /{id} - 获取项目详情
- PUT /{id} - 更新项目
- DELETE /{id} - 删除项目
- POST /{id}/save - 保存项目草稿
- GET /{id}/versions - 获取项目版本历史
- POST /{id}/duplicate - 复制项目

### 模板接口 (/api/v1/templates)
- GET / - 获取模板列表
- GET /{id} - 获取模板详情
- POST /{id}/use - 使用模板创建项目
- GET /categories - 获取模板分类

### 文件接口 (/api/v1/files)
- POST /upload - 上传文件
- GET /{id} - 获取文件信息
- DELETE /{id} - 删除文件
- GET /my-files - 获取用户文件列表

## 🚀 核心功能实现

### 1. 自动草稿保存
- WebSocket连接实时同步
- 防抖机制避免频繁保存
- 版本控制支持历史回滚

### 2. 文件上传优化
- 分片上传支持大文件
- 图片自动压缩和格式转换
- CDN加速文件访问

### 3. 模板系统
- 模板分类和标签管理
- 用户自定义模板
- 模板使用统计和推荐

### 4. 性能优化
- Redis缓存热点数据
- 数据库查询优化
- 异步任务处理

## 🔧 开发与部署

### 开发环境
```bash
# 安装依赖
pip install -r requirements.txt

# 数据库迁移
aerich init -t app.core.database.TORTOISE_ORM
aerich init-db
aerich migrate
aerich upgrade

# 启动开发服务器
python -m app.main
```

### 生产部署
- Docker容器化部署
- Nginx反向代理
- PostgreSQL数据库
- Redis缓存集群
- 文件存储CDN

这个架构设计充分借鉴了React+FastAPI管理系统的优秀实践，同时针对Canva项目的特殊需求进行了优化，确保系统的可扩展性、性能和安全性。
