# 🔍 422错误详细调试指南

## ✅ 认证问题已解决

从你的反馈可以确认：
- ✅ Token同步正常工作
- ✅ 调试面板显示认证状态正常
- ❌ 但项目创建仍然返回422错误

## 🔧 已增强的调试功能

我已经添加了详细的调试日志来帮助定位422错误的具体原因：

### 1. **项目创建Hook增强**
- 详细的认证状态检查
- 完整的错误信息记录
- 请求数据的详细日志

### 2. **Project Service增强**
- 发送前的完整数据日志
- 失败时的数据记录

### 3. **API客户端增强**
- 详细的错误响应解析
- 422错误的具体详情

## 🧪 详细测试步骤

### 1. 打开浏览器开发者工具
- 按F12打开开发者工具
- 切换到Console标签页
- 清除之前的日志

### 2. 尝试创建项目
1. 点击 "Start creating" 按钮
2. 观察控制台中的详细日志

### 3. 预期看到的详细日志

#### 认证检查阶段
```
🔍 Checking auth status before creating project...
🔍 Detailed auth check: {
  hasAccessToken: true,
  accessTokenLength: 200,
  accessTokenPreview: "eyJ0eXAiOiJKV1QiLCJhbGciOi...",
  hasRefreshToken: true,
  hasExpires: true,
  expires: "2025-08-13T...",
  isExpired: false,
  allKeys: ["canva_access_token", "canva_refresh_token", "canva_token_expires"]
}
✅ Auth status OK, proceeding with project creation
```

#### 项目数据准备阶段
```
📊 Project data to create: {
  title: "Untitled project",
  canvas_data: {
    version: "5.3.0",
    objects: []
  },
  width: 900,
  height: 1200
}
🆕 Creating new project: Untitled project
📋 Full project data: {
  "title": "Untitled project",
  "canvas_data": {
    "version": "5.3.0",
    "objects": []
  },
  "width": 900,
  "height": 1200
}
```

#### API调用阶段
```
🔑 Auth token check: {
  hasToken: true,
  tokenLength: 200,
  tokenPreview: "eyJ0eXAiOiJKV1QiLCJhbGciOi..."
}
🔐 Token header added
🔄 POST Request: http://localhost:8000/api/v1/canva/projects/
```

#### 错误详情（如果失败）
```
❌ API Error Details: {
  status: 422,
  url: "http://localhost:8000/api/v1/canva/projects/",
  errorData: {
    detail: [
      {
        "loc": ["body", "field_name"],
        "msg": "具体的验证错误信息",
        "type": "validation_error"
      }
    ]
  }
}
💥 Project creation failed with error: HTTP 422
💥 Error details: {
  message: "HTTP 422",
  projectData: { ... }
}
```

## 🎯 关键信息收集

请在测试时特别关注以下信息：

### 1. **认证头信息**
- Token是否正确添加到请求头
- Token长度和格式是否正确

### 2. **请求数据格式**
- 发送的JSON数据结构
- 字段名称和类型是否匹配后端期望

### 3. **具体的422错误详情**
- `errorData.detail`中的具体验证错误
- 哪个字段导致了验证失败
- 错误类型和消息

## 🚨 可能的422错误原因

### 1. **数据验证失败**
- 字段类型不匹配（如字符串vs数字）
- 必填字段缺失
- 字段值超出范围限制

### 2. **认证头格式问题**
- Token格式不正确
- 认证头名称不匹配
- Token过期或无效

### 3. **请求体格式问题**
- JSON格式错误
- Content-Type头不正确
- 字符编码问题

## 🔍 后端验证规则

根据后端代码，项目创建的验证规则：

```python
class ProjectCreate(ProjectBase):
    title: str = Field(..., min_length=1, max_length=200)  # 必填，1-200字符
    description: Optional[str] = Field(None)               # 可选
    width: int = Field(default=800, ge=100, le=5000)       # 100-5000
    height: int = Field(default=600, ge=100, le=5000)      # 100-5000
    is_public: bool = Field(default=False)                 # 布尔值
    is_template: bool = Field(default=False)               # 布尔值
    canvas_data: Optional[Dict[str, Any]] = Field(None)    # 可选字典
```

### 验证检查清单
- [ ] title: 字符串，1-200字符 ✅
- [ ] width: 数字，100-5000 ✅ (900)
- [ ] height: 数字，100-5000 ✅ (1200)
- [ ] canvas_data: 字典格式 ✅

## 📋 请提供的信息

测试后，请提供以下信息：

1. **完整的控制台日志**（特别是错误部分）
2. **网络面板中的请求详情**：
   - 请求头（特别是token头）
   - 请求体内容
   - 响应状态和内容
3. **后端日志中的具体错误信息**

## 🎯 下一步

根据你提供的详细错误信息，我将能够：

1. **精确定位问题**：确定是数据格式、认证还是其他问题
2. **提供针对性修复**：修复具体的验证错误
3. **完善错误处理**：改进错误提示和用户体验

让我们获取这些详细的调试信息来解决422错误！🔍✨
