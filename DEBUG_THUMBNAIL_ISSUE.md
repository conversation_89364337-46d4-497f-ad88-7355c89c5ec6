# 🔍 缩略图问题调试指南

## ✅ 已修复的问题

### 1. **ReferenceError: mutate is not defined**
- ✅ 修复了editor.tsx中的依赖数组错误
- ✅ 将`[mutate]`改为`[autoSave]`

## 🧪 调试步骤

### 1. **测试编辑器加载**
1. 创建新项目或打开现有项目
2. 确认编辑器正常加载，没有错误

#### 预期结果
- ✅ 编辑器正常显示
- ✅ 工具栏显示保存按钮
- ✅ 没有"mutate is not defined"错误

### 2. **测试缩略图生成**
1. 在编辑器中添加一些元素（文本、形状、图片）
2. 等待自动保存触发（500ms后）
3. 观察控制台日志

#### 预期日志
```
📸 Generating thumbnail for canvas: 900 x 1200
📸 Thumbnail multiplier: 0.16666666666666666
✅ Thumbnail generated successfully, length: 12345
📸 Thumbnail result: Generated
💾 Auto-saving project: 1
💾 Auto-save data: {
  canvas_data: "Has canvas data",
  thumbnail_url: "Has thumbnail", 
  thumbnail_length: 12345,
  width: 900,
  height: 1200
}
✅ Auto-save successful
```

### 3. **测试项目列表显示**
1. 完成编辑后返回首页
2. 查看"Recent projects"部分
3. 观察控制台日志

#### 预期日志
```
🔍 useGetProjects: Starting to fetch projects
✅ useGetProjects: Successfully fetched projects: {
  count: 1,
  projects: [
    { id: 1, title: "Untitled project", updated_at: "2025-01-12T..." }
  ]
}
📋 Projects data for display: [
  {
    id: 1,
    title: "Untitled project",
    thumbnail_url: "Has thumbnail",
    thumbnail_length: 12345
  }
]
```

## 🚨 可能的问题和解决方案

### 问题1: 缩略图生成失败
**症状**: 控制台显示"❌ Failed to generate thumbnail"

**可能原因**:
- Canvas尺寸为0
- Canvas未正确初始化
- toDataURL方法失败

**调试步骤**:
```javascript
// 在浏览器控制台检查
console.log('Canvas size:', canvas.getWidth(), 'x', canvas.getHeight());
console.log('Canvas objects:', canvas.getObjects().length);
```

### 问题2: 缩略图未保存到数据库
**症状**: 生成成功但项目列表中没有缩略图

**检查步骤**:
1. 确认自动保存日志中有"Has thumbnail"
2. 检查网络面板中的PUT请求
3. 验证后端是否正确处理thumbnail_url字段

### 问题3: 项目列表不显示缩略图
**症状**: 数据有缩略图但界面不显示

**检查步骤**:
```javascript
// 检查项目数据
console.log('Project thumbnail_url:', project.thumbnail_url);
console.log('Thumbnail URL type:', typeof project.thumbnail_url);
```

## 🔧 手动测试步骤

### 1. **创建测试项目**
```
1. 点击 "Start creating"
2. 添加文本: "Test Project"
3. 添加一个矩形
4. 等待自动保存
5. 返回首页检查
```

### 2. **检查数据库**
如果有数据库访问权限，检查项目表：
```sql
SELECT id, title, thumbnail_url FROM projects ORDER BY updated_at DESC LIMIT 5;
```

### 3. **手动API测试**
```javascript
// 在浏览器控制台测试
fetch('http://localhost:8000/api/v1/canva/projects/', {
  headers: {
    'token': localStorage.getItem('canva_access_token')
  }
})
.then(r => r.json())
.then(data => console.log('API Response:', data));
```

## 🎯 成功标准

### ✅ 编辑器功能
- [ ] 编辑器正常加载，无错误
- [ ] 自动保存正常工作
- [ ] 缩略图生成成功

### ✅ 缩略图功能
- [ ] 控制台显示缩略图生成日志
- [ ] 自动保存包含缩略图数据
- [ ] 项目列表显示缩略图

### ✅ 用户体验
- [ ] 项目卡片显示内容预览
- [ ] 缩略图加载快速
- [ ] 界面美观一致

## 📋 下一步

如果问题持续存在，请提供：

1. **完整的控制台日志**（特别是缩略图生成部分）
2. **网络面板截图**（项目更新请求）
3. **项目列表的数据内容**（控制台中的项目数据）

这样我就能精确定位问题并提供针对性的解决方案！

## 🔍 快速检查清单

- [ ] 编辑器加载无错误
- [ ] 控制台有缩略图生成日志
- [ ] 自动保存包含缩略图数据
- [ ] 项目列表API返回缩略图
- [ ] 项目卡片显示缩略图

让我们一步步解决这些问题！🎨✨
