# 🧪 FastAPI路由修复测试指南

## ✅ 已实施的修复

### 🔧 环境变量配置
已添加到`.env.local`:
```env
NEXT_PUBLIC_USE_FASTAPI=true
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### 🚫 本地API禁用
修改了`src/app/api/[[...route]]/route.ts`，当`NEXT_PUBLIC_USE_FASTAPI=true`时：
- 返回404状态码
- 显示"Local API disabled"消息
- 强制使用FastAPI后端

## 🧪 测试步骤

### 1. 重启前端服务
```bash
# 停止当前服务 (Ctrl+C)
# 重新启动以加载新的环境变量
npm run dev
```

### 2. 验证本地API禁用
访问: http://localhost:3000/api/projects
应该看到:
```json
{
  "error": "Local API disabled",
  "message": "Using FastAPI backend at http://localhost:8000"
}
```

### 3. 清除浏览器缓存
```javascript
// 在浏览器控制台运行
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### 4. 重新登录测试
1. 访问: http://localhost:3000/sign-in
2. 输入: `demo` / `demo123`
3. 观察控制台日志

#### 预期登录日志
```
🔐 Attempting login for: demo
✅ Login successful
🎫 Token received from FastAPI backend
🔍 Parsed JWT payload: { user_id: 2, username: 'demo', ... }
✅ FastAPI Login successful
```

### 5. 测试项目创建
1. 点击 "Start creating" 按钮
2. 观察网络面板和控制台

#### 预期项目创建日志
```
🔍 Checking auth status before creating project...
✅ User is authenticated
✅ Auth status OK, proceeding with project creation
🔑 Auth token check: { hasToken: true, tokenLength: 200, ... }
🔐 Token header added
🔄 POST Request: http://localhost:8000/api/v1/canva/projects/
✅ API Response: { status: 200, ... }
🆕 Creating new project: Untitled project
✅ Project created successfully
```

#### 不应该看到的日志
```
❌ Using mock database for local development
❌ 🔄 Projects API loaded
❌ GET /api/projects
❌ POST /api/projects
```

## 🔍 网络面板检查

### ✅ 应该看到的请求
- `POST http://localhost:8000/api/v1/base/access_token` (登录)
- `POST http://localhost:8000/api/v1/canva/projects/` (创建项目)

### ❌ 不应该看到的请求
- `POST http://localhost:3000/api/projects`
- `GET http://localhost:3000/api/projects`

## 🎯 成功标准

### ✅ 登录功能
- [ ] JWT token正确获取
- [ ] Token存储到localStorage
- [ ] 用户信息正确解析
- [ ] 页面跳转到首页

### ✅ 项目创建功能
- [ ] 认证状态检查通过
- [ ] 调用FastAPI后端成功
- [ ] 返回200状态码
- [ ] 项目ID正确返回
- [ ] 跳转到编辑器页面

### ✅ 错误处理
- [ ] 本地API返回404
- [ ] 认证失败时正确处理
- [ ] 用户友好的错误提示

## 🚨 故障排除

### 问题1: 仍然看到mock数据库日志
**解决方案**: 
1. 确认环境变量正确设置
2. 重启前端服务
3. 清除浏览器缓存

### 问题2: 项目创建仍然失败
**检查步骤**:
1. 确认FastAPI后端正在运行
2. 检查网络面板中的请求URL
3. 验证认证头是否正确发送

### 问题3: 认证失败
**检查步骤**:
1. 确认token正确存储
2. 检查token格式和过期时间
3. 验证后端JWT配置

## 🔄 回滚方案

如果需要回滚到本地API:
```env
# .env.local
NEXT_PUBLIC_USE_FASTAPI=false
```

然后重启服务:
```bash
npm run dev
```

## 🎉 预期最终结果

修复成功后，你的Canva克隆应用将：

1. **完全使用FastAPI后端**: 所有API调用指向http://localhost:8000
2. **正常认证**: JWT token管理正常工作
3. **项目管理**: 创建、编辑、保存项目功能正常
4. **编辑器功能**: Fabric.js编辑器正常工作
5. **自动保存**: 画布数据自动保存到FastAPI后端

## 📋 下一步测试

修复后，继续测试以下功能：

1. **项目列表**: 显示从FastAPI获取的项目
2. **项目编辑**: 打开现有项目进行编辑
3. **自动保存**: 编辑器中的自动保存功能
4. **项目删除**: 删除项目功能
5. **用户管理**: 用户信息和设置

让我们开始测试这个修复！🎨✨
