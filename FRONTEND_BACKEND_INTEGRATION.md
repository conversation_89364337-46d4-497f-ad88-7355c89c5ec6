# Canva克隆项目前后端集成指南

## 📋 项目概述

本文档详细说明如何将基于 **react-fastapi-admin** 开源项目定制的FastAPI后端与现有的Canva克隆前端项目进行集成，实现完整的全栈应用。

## 🎯 架构特点

我们基于成熟的 `react-fastapi-admin` 开源项目进行定制开发，具有以下优势：

- ✅ **成熟稳定**: 基于经过验证的开源架构
- 🔐 **完整认证**: JWT认证 + 权限管理系统
- 📊 **管理后台**: 内置完整的管理界面
- 🚀 **高性能**: FastAPI + Tortoise ORM异步架构
- 🛡️ **安全可靠**: 完善的安全机制和中间件
- 📝 **文档完整**: 自动生成的API文档

## 🏗️ 架构概览

```
canva-clone-main/
├── src/                    # Next.js前端项目
│   ├── app/               # App Router
│   ├── features/          # 功能模块
│   ├── components/        # 组件
│   └── lib/               # 工具库
├── canva-backend/         # FastAPI后端项目
│   ├── app/               # 后端应用
│   ├── migrations/        # 数据库迁移
│   └── uploads/           # 文件上传
└── README.md              # 项目说明
```

## 🚀 快速开始

### 1. 后端设置

```bash
# 进入后端目录
cd canva-backend

# 方式一：使用自动启动脚本（推荐）
python start_canva.py

# 方式二：手动设置
# 1. 复制环境配置
cp .env.canva .env

# 2. 安装依赖
pip install -r requirements.txt

# 3. 初始化数据库
python init_canva_db.py

# 4. 启动服务
python main.py
```

**自动启动脚本会自动完成以下操作：**
- ✅ 检查Python版本和依赖
- ✅ 复制环境配置文件
- ✅ 初始化数据库和创建管理员账户
- ✅ 启动开发服务器

后端服务将在 http://localhost:8000 启动

### 2. 前端设置

```bash
# 返回项目根目录
cd ..

# 安装前端依赖
npm install

# 启动前端开发服务器
npm run dev
```

前端服务将在 http://localhost:3000 启动

## 🔗 API集成

### 1. 创建API客户端

在前端项目中创建API客户端配置：

```typescript
// src/lib/api-client.ts
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器 - 处理错误
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，重定向到登录页
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### 2. 认证服务

```typescript
// src/lib/auth-service.ts
import { apiClient } from './api-client';

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  is_premium: boolean;
}

export const authService = {
  async login(credentials: LoginCredentials) {
    const response = await apiClient.post('/auth/login', credentials);
    const { access_token, refresh_token } = response.data;
    
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    
    return response.data;
  },

  async register(userData: RegisterData) {
    const response = await apiClient.post('/auth/register', userData);
    return response.data;
  },

  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get('/auth/me');
    return response.data;
  },

  async logout() {
    await apiClient.post('/auth/logout');
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  },

  async refreshToken() {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) throw new Error('No refresh token');

    const response = await apiClient.post('/auth/refresh', {}, {
      headers: { Authorization: `Bearer ${refreshToken}` }
    });
    
    const { access_token } = response.data;
    localStorage.setItem('access_token', access_token);
    
    return access_token;
  }
};
```

### 3. 项目服务

```typescript
// src/lib/project-service.ts
import { apiClient } from './api-client';

export interface Project {
  id: number;
  title: string;
  description?: string;
  canvas_data: any;
  thumbnail_url?: string;
  width: number;
  height: number;
  status: 'draft' | 'published' | 'archived';
  created_at: string;
  updated_at: string;
}

export const projectService = {
  async getProjects() {
    const response = await apiClient.get('/projects/');
    return response.data;
  },

  async getProject(id: number): Promise<Project> {
    const response = await apiClient.get(`/projects/${id}`);
    return response.data;
  },

  async createProject(projectData: Partial<Project>): Promise<Project> {
    const response = await apiClient.post('/projects/', projectData);
    return response.data;
  },

  async updateProject(id: number, projectData: Partial<Project>): Promise<Project> {
    const response = await apiClient.put(`/projects/${id}`, projectData);
    return response.data;
  },

  async deleteProject(id: number) {
    await apiClient.delete(`/projects/${id}`);
  },

  async saveProject(id: number, canvasData: any) {
    const response = await apiClient.post(`/projects/${id}/save`, {
      canvas_data: canvasData
    });
    return response.data;
  }
};
```

## 🔐 认证集成

### 1. 更新NextAuth配置

修改 `src/auth.ts` 以集成后端认证：

```typescript
// src/auth.ts
import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { authService } from "./lib/auth-service";

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          const result = await authService.login({
            username: credentials.username as string,
            password: credentials.password as string,
          });
          
          const user = await authService.getCurrentUser();
          
          return {
            id: user.id.toString(),
            name: user.username,
            email: user.email,
            image: user.avatar_url,
          };
        } catch (error) {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
});
```

### 2. 创建认证Hook

```typescript
// src/hooks/use-auth.ts
import { useState, useEffect } from 'react';
import { authService, User } from '@/lib/auth-service';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (token) {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('access_token');
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials: { username: string; password: string }) => {
    try {
      await authService.login(credentials);
      await checkAuth();
      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user,
  };
}
```

## 💾 数据持久化

### 1. 项目自动保存

修改编辑器Hook以支持自动保存：

```typescript
// src/features/editor/hooks/use-editor.ts
import { useCallback, useEffect } from 'react';
import { projectService } from '@/lib/project-service';
import { useDebounce } from '@/hooks/use-debounce';

export const useEditor = () => {
  // ... 现有代码

  const [projectId, setProjectId] = useState<number | null>(null);
  const debouncedCanvasData = useDebounce(canvasData, 2000); // 2秒防抖

  // 自动保存
  useEffect(() => {
    if (projectId && debouncedCanvasData) {
      saveProject();
    }
  }, [debouncedCanvasData, projectId]);

  const saveProject = useCallback(async () => {
    if (!projectId || !canvas) return;

    try {
      const canvasData = canvas.toJSON();
      await projectService.saveProject(projectId, canvasData);
      console.log('Project saved automatically');
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  }, [projectId, canvas]);

  const loadProject = useCallback(async (id: number) => {
    try {
      const project = await projectService.getProject(id);
      setProjectId(id);
      
      // 加载画布数据
      if (canvas && project.canvas_data) {
        canvas.loadFromJSON(project.canvas_data, () => {
          canvas.renderAll();
        });
      }
    } catch (error) {
      console.error('Failed to load project:', error);
    }
  }, [canvas]);

  return {
    // ... 现有返回值
    saveProject,
    loadProject,
    projectId,
  };
};
```

## 📁 文件上传集成

### 1. 文件上传服务

```typescript
// src/lib/file-service.ts
import { apiClient } from './api-client';

export const fileService = {
  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  },

  async getFiles() {
    const response = await apiClient.get('/files/');
    return response.data;
  },

  async deleteFile(fileId: number) {
    await apiClient.delete(`/files/${fileId}`);
  },
};
```

## 🌐 环境配置

### 1. 前端环境变量

创建 `.env.local` 文件：

```env
# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000

# NextAuth配置
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
```

### 2. 后端CORS配置

确保后端 `.env` 文件包含前端URL：

```env
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
```

## 🧪 测试集成

### 1. 测试认证流程

1. 启动后端服务：`cd canva-backend && python main.py`
2. 启动前端服务：`npm run dev`
3. 访问 http://localhost:3000
4. 测试注册、登录功能
5. 验证API调用是否正常

### 2. 测试项目保存

1. 登录后创建新项目
2. 在编辑器中添加元素
3. 验证自动保存功能
4. 刷新页面确认数据持久化

## 📝 下一步计划

1. ✅ 基础认证集成
2. ✅ 项目数据持久化
3. 🔄 文件上传功能
4. 🔄 模板系统集成
5. 🔄 实时协作功能
6. 🔄 性能优化

## 🐛 常见问题

### CORS错误
确保后端CORS配置包含前端URL，重启后端服务。

### 认证失败
检查JWT密钥配置，确保前后端使用相同的密钥。

### 数据库连接错误
确保数据库文件权限正确，运行数据库迁移。

## 🤝 贡献指南

1. 遵循现有代码风格
2. 添加适当的错误处理
3. 编写测试用例
4. 更新文档

这个集成方案提供了完整的前后端连接，支持用户认证、项目管理和数据持久化等核心功能。
