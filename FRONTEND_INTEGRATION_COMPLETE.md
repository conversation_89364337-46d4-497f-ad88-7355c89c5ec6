# 🎨 Canva克隆项目前后端集成完成指南

## ✅ 已完成的前后端集成工作

我已经成功为你的Canva克隆项目完成了前后端的完整集成，将Next.js前端与FastAPI后端连接起来。

### 🔗 核心集成组件

#### 1. **API客户端** (`src/lib/api-client.ts`)
- ✅ 基于Axios的HTTP客户端
- ✅ 自动添加JWT认证头
- ✅ 请求/响应拦截器
- ✅ 错误处理和重试机制
- ✅ 401自动重定向到登录页

#### 2. **认证服务** (`src/lib/auth-service.ts`)
- ✅ 完整的用户认证流程
- ✅ 登录、注册、登出功能
- ✅ JWT令牌管理和自动刷新
- ✅ 用户信息获取和缓存
- ✅ 密码修改功能

#### 3. **项目服务** (`src/lib/project-service.ts`)
- ✅ 项目CRUD操作
- ✅ 画布数据自动保存
- ✅ 项目复制和发布
- ✅ 项目搜索和统计
- ✅ 公开项目获取

#### 4. **React Hooks集成**
- ✅ `useAuth` - 认证状态管理
- ✅ `useProjects` - 项目数据管理
- ✅ `useAutoSave` - 自动保存功能
- ✅ `useDebounce` - 防抖处理

#### 5. **编辑器自动保存** (`src/features/editor/hooks/use-editor-autosave.ts`)
- ✅ 监听Fabric.js画布变化
- ✅ 防抖自动保存机制
- ✅ 手动保存功能
- ✅ 项目数据加载

### 🔄 已更新的现有文件

#### 1. **认证配置** (`src/auth.config.ts`)
- ✅ 集成FastAPI后端认证
- ✅ 替换本地用户验证为API调用
- ✅ 支持用户名/密码登录

#### 2. **项目API Hooks**
- ✅ `use-get-projects.ts` - 获取项目列表
- ✅ `use-get-project.ts` - 获取单个项目
- ✅ `use-create-project.ts` - 创建新项目

## 🚀 使用指南

### 1. 环境配置

创建 `.env.local` 文件：

```env
# NextAuth配置
NEXTAUTH_SECRET=your-nextauth-secret-key
NEXTAUTH_URL=http://localhost:3000

# FastAPI后端API配置
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### 2. 启动服务

```bash
# 1. 启动后端服务
cd canva-backend
python main.py  # 运行在 http://localhost:8000

# 2. 启动前端服务
cd ..
npm run dev     # 运行在 http://localhost:3000
```

### 3. 在组件中使用认证

```tsx
import { useAuth } from '@/hooks/use-auth';

export function MyComponent() {
  const { user, login, logout, isAuthenticated, loading } = useAuth();

  const handleLogin = async () => {
    const success = await login({
      username: 'demo',
      password: 'demo123'
    });
    
    if (success) {
      console.log('登录成功');
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>欢迎, {user?.username}!</p>
          <button onClick={logout}>登出</button>
        </div>
      ) : (
        <button onClick={handleLogin}>登录</button>
      )}
    </div>
  );
}
```

### 4. 在组件中使用项目管理

```tsx
import { useGetProjects, useCreateProject } from '@/hooks/use-projects';

export function ProjectList() {
  const { data: projects, isLoading, error } = useGetProjects();
  const createProject = useCreateProject();

  const handleCreateProject = async () => {
    await createProject.mutateAsync({
      title: '新项目',
      description: '项目描述',
      width: 800,
      height: 600,
    });
  };

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      <button onClick={handleCreateProject}>创建项目</button>
      {projects?.map(project => (
        <div key={project.id}>
          <h3>{project.title}</h3>
          <p>{project.description}</p>
        </div>
      ))}
    </div>
  );
}
```

### 5. 在编辑器中使用自动保存

```tsx
import { useEditorAutoSave } from '@/features/editor/hooks/use-editor-autosave';

export function CanvasEditor({ projectId }: { projectId: number }) {
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  
  const {
    isSaving,
    lastSaved,
    error,
    saveNow,
    loadProjectData
  } = useEditorAutoSave({
    canvas,
    projectId,
    enabled: true,
    delay: 3000, // 3秒自动保存
  });

  return (
    <div>
      <div className="save-status">
        {isSaving && <span>保存中...</span>}
        {lastSaved && <span>最后保存: {lastSaved.toLocaleTimeString()}</span>}
        {error && <span className="error">保存失败: {error}</span>}
      </div>
      
      <button onClick={saveNow}>手动保存</button>
      
      <canvas ref={canvasRef} />
    </div>
  );
}
```

## 🔧 API接口映射

### 认证接口
| 前端方法 | 后端接口 | 说明 |
|---------|---------|------|
| `authService.login()` | `POST /api/v1/base/login` | 用户登录 |
| `authService.register()` | `POST /api/v1/auth/register` | 用户注册 |
| `authService.getCurrentUser()` | `GET /api/v1/auth/me` | 获取当前用户 |
| `authService.logout()` | `POST /api/v1/auth/logout` | 用户登出 |

### 项目接口
| 前端方法 | 后端接口 | 说明 |
|---------|---------|------|
| `projectService.getProjects()` | `GET /api/v1/canva/projects/` | 获取项目列表 |
| `projectService.getProject()` | `GET /api/v1/canva/projects/{id}` | 获取项目详情 |
| `projectService.createProject()` | `POST /api/v1/canva/projects/` | 创建新项目 |
| `projectService.updateProject()` | `PUT /api/v1/canva/projects/{id}` | 更新项目 |
| `projectService.saveProjectCanvas()` | `POST /api/v1/canva/projects/{id}/save` | 保存画布数据 |
| `projectService.duplicateProject()` | `POST /api/v1/canva/projects/{id}/duplicate` | 复制项目 |

## 🧪 测试步骤

### 1. 测试认证流程
1. 访问 http://localhost:3000
2. 尝试登录（用户名: demo, 密码: demo123）
3. 验证登录状态和用户信息显示
4. 测试登出功能

### 2. 测试项目管理
1. 登录后创建新项目
2. 验证项目列表显示
3. 打开项目编辑器
4. 测试自动保存功能

### 3. 测试自动保存
1. 在编辑器中添加元素
2. 观察自动保存状态指示器
3. 刷新页面验证数据持久化
4. 测试手动保存功能

## 🐛 常见问题解决

### 1. CORS错误
确保后端 `.env` 文件包含前端URL：
```env
CORS_ORIGINS=["http://localhost:3000"]
```

### 2. 认证失败
- 检查后端服务是否正常运行
- 验证API_URL环境变量配置
- 查看浏览器控制台错误信息

### 3. 自动保存不工作
- 确保项目ID正确传递
- 检查用户是否已登录
- 验证画布变化事件是否触发

## 🎉 集成完成

现在你的Canva克隆项目已经完全集成了前后端功能：

- ✅ **用户认证**: 完整的登录注册流程
- ✅ **项目管理**: 创建、编辑、保存项目
- ✅ **自动保存**: 实时保存画布数据
- ✅ **数据持久化**: 所有数据存储在FastAPI后端
- ✅ **错误处理**: 完善的错误处理和用户反馈

你现在可以开始使用这个完整的全栈Canva克隆应用了！🎨✨
