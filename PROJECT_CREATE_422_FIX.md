# 🔧 项目创建422错误修复

## ✅ 问题诊断

### 🔍 错误分析
从终端日志可以看到：

1. **登录成功**: JWT token正确获取和存储
2. **项目创建失败**: 422错误，表示请求验证失败
3. **关键问题**: 在项目创建时，前端显示`hasToken: false`

### 📊 错误流程
```
1. 用户登录成功 ✅
2. Token存储到localStorage ✅
3. 用户点击创建项目 ✅
4. 前端调用FastAPI后端 ❌
5. 认证头缺失或无效 ❌
6. 后端返回422错误 ❌
```

### 🔍 关键日志证据
```
🔑 Auth token check: { hasToken: false, tokenLength: 0, tokenPreview: 'null' }
⚠️ No auth token found in localStorage
POST /api/v1/canva/projects/ - 422 Unprocessable Entity
```

## 🔧 问题根源

### 1. **Token读取时机问题**
在项目创建时，`getAuthToken()`函数返回null，表明localStorage中的token无法正确读取。

### 2. **可能的原因**
- localStorage在某些情况下可能被清除
- 浏览器安全策略限制
- 异步操作时序问题
- NextAuth会话管理冲突

## 🛠️ 修复方案

### 方案1: 增强Token检查和重试机制

```typescript
// 在api-client.ts中增强token获取逻辑
function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  
  // 尝试多个可能的存储位置
  const token = localStorage.getItem('canva_access_token') ||
                sessionStorage.getItem('canva_access_token');
  
  if (!token) {
    console.warn('⚠️ No auth token found, user may need to re-login');
  }
  
  return token;
}
```

### 方案2: 使用NextAuth会话中的token

```typescript
// 在项目创建时，从NextAuth会话中获取token
import { useSession } from 'next-auth/react';

export const useCreateProject = () => {
  const { data: session } = useSession();
  
  const mutation = useMutation({
    mutationFn: async (projectData) => {
      // 确保用户已登录
      if (!session?.user) {
        throw new Error('用户未登录');
      }
      
      return await projectService.createProject(projectData);
    },
    // ...
  });
};
```

### 方案3: 手动重新登录检查

```typescript
// 在API调用失败时，检查登录状态
const handleApiError = (error: any) => {
  if (error.message.includes('422') || error.message.includes('401')) {
    // 检查是否需要重新登录
    const token = localStorage.getItem('canva_access_token');
    if (!token) {
      console.log('🔐 Token missing, redirecting to login');
      window.location.href = '/sign-in';
    }
  }
};
```

## 🧪 测试步骤

### 1. 清除所有存储
```javascript
// 在浏览器控制台运行
localStorage.clear();
sessionStorage.clear();
```

### 2. 重新登录
1. 访问: http://localhost:3000/sign-in
2. 输入: 用户名 `demo`, 密码 `demo123`
3. 确认登录成功

### 3. 检查Token存储
```javascript
// 在浏览器控制台检查
console.log('Access Token:', localStorage.getItem('canva_access_token'));
console.log('Refresh Token:', localStorage.getItem('canva_refresh_token'));
```

### 4. 测试项目创建
1. 点击 "Start creating" 按钮
2. 观察浏览器控制台日志
3. 检查网络面板中的请求头

## 🔍 调试检查清单

### ✅ Token存储检查
- [ ] localStorage中有canva_access_token
- [ ] Token格式正确（JWT格式）
- [ ] Token未过期

### ✅ 请求头检查
- [ ] 请求包含token头
- [ ] Token值正确传递
- [ ] 请求Content-Type正确

### ✅ 后端验证
- [ ] 后端收到认证头
- [ ] JWT token验证通过
- [ ] 用户权限验证通过

## 🎯 预期修复结果

### 成功的项目创建流程
```
🔐 Attempting login for: demo
✅ Login successful
💾 Tokens stored in localStorage

🔑 Auth token check: { hasToken: true, tokenLength: 200, tokenPreview: 'eyJ0eXAiOiJKV1Q...' }
🔐 Token header added
🔄 POST Request: http://localhost:8000/api/v1/canva/projects/
✅ API Response: { status: 200, url: '...', ok: true }
🆕 Creating new project: Untitled project
✅ Project created successfully: 1
```

## 🚀 临时解决方案

如果问题持续存在，可以使用以下临时解决方案：

### 1. 强制重新登录
```typescript
const handleCreateProject = async () => {
  // 检查token是否存在
  const token = localStorage.getItem('canva_access_token');
  if (!token) {
    alert('登录已过期，请重新登录');
    window.location.href = '/sign-in';
    return;
  }
  
  // 继续创建项目
  mutation.mutate(projectData);
};
```

### 2. 使用会话存储备份
```typescript
// 在登录成功后，同时存储到sessionStorage
localStorage.setItem('canva_access_token', token);
sessionStorage.setItem('canva_access_token', token);
```

### 3. 添加重试机制
```typescript
const createProjectWithRetry = async (projectData, retries = 1) => {
  try {
    return await projectService.createProject(projectData);
  } catch (error) {
    if (error.message.includes('422') && retries > 0) {
      // 重新获取token并重试
      await refreshAuthToken();
      return createProjectWithRetry(projectData, retries - 1);
    }
    throw error;
  }
};
```

## 🎉 预期结果

修复后，用户应该能够：

1. **正常登录**: 获取并存储JWT token
2. **创建项目**: 成功调用后端API
3. **自动保存**: 编辑器功能正常工作
4. **持久化**: 数据正确存储到数据库

让我们实施这些修复方案并测试结果！🎨✨
