# 📋 项目列表显示问题调试指南

## ✅ 已实施的修复

### 🔧 ProjectsSection组件更新
我已经将静态的`ProjectsSection`组件更新为动态组件：

1. **集成API调用**: 使用`useGetProjects` Hook获取项目数据
2. **加载状态**: 显示加载动画和错误状态
3. **项目展示**: 网格布局显示项目卡片
4. **交互功能**: 点击打开项目，下拉菜单操作

### 🔍 增强调试日志
在`useGetProjects` Hook中添加了详细的调试信息：

```typescript
console.log('🔍 useGetProjects: Starting to fetch projects');
console.log('✅ useGetProjects: Successfully fetched projects:', {
  count: projects.length,
  projects: projects.map(p => ({ id: p.id, title: p.title, updated_at: p.updated_at }))
});
console.log('🔍 useGetProjects query state:', {
  isLoading, isError, error, dataLength, status
});
```

## 🧪 测试步骤

### 1. 检查项目列表加载
1. 访问首页: http://localhost:3000
2. 观察"Recent projects"部分
3. 查看控制台日志

#### 预期行为

**加载中状态**:
```
Recent projects
[Loading spinner]
Loading your projects...
```

**成功加载**:
```
Recent projects
[项目网格显示，每个项目显示缩略图、标题、更新时间]
```

**空状态**:
```
Recent projects
[文件夹图标]
Your projects will appear here
Create a new project to get started
```

### 2. 观察控制台日志

#### 预期日志（成功）
```
🔍 useGetProjects: Starting to fetch projects with params: undefined
📋 Fetching projects with params: undefined
🔑 Auth token check: { hasToken: true, ... }
🔐 Token header added
🔄 GET Request: http://localhost:8000/api/v1/canva/projects/
✅ API Response: { status: 200, ... }
✅ Fetched 2 projects
✅ useGetProjects: Successfully fetched projects: {
  count: 2,
  projects: [
    { id: 1, title: "Untitled project", updated_at: "2025-01-12T..." },
    { id: 2, title: "My Design", updated_at: "2025-01-12T..." }
  ]
}
🔍 useGetProjects query state: {
  isLoading: false,
  isError: false,
  error: undefined,
  dataLength: 2,
  status: "success"
}
```

#### 预期日志（空列表）
```
🔍 useGetProjects: Starting to fetch projects
✅ Fetched 0 projects
✅ useGetProjects: Successfully fetched projects: { count: 0, projects: [] }
🔍 useGetProjects query state: { isLoading: false, isError: false, dataLength: 0, status: "success" }
```

#### 预期日志（错误）
```
🔍 useGetProjects: Starting to fetch projects
❌ Failed to fetch projects: HTTP 401/422/500
❌ useGetProjects: Failed to fetch projects: Error message
🔍 useGetProjects query state: { isLoading: false, isError: true, error: "Error message", status: "error" }
```

### 3. 检查网络面板
1. 打开开发者工具 → Network标签
2. 刷新页面
3. 查找对`/api/v1/canva/projects/`的GET请求

#### 预期网络请求
- **URL**: `http://localhost:8000/api/v1/canva/projects/`
- **Method**: GET
- **Headers**: 包含`token`认证头
- **Status**: 200 OK
- **Response**: JSON数组包含项目列表

## 🚨 可能的问题和解决方案

### 问题1: 显示"Loading your projects..."但一直不变
**可能原因**:
- API请求失败
- 认证问题
- 网络连接问题

**调试步骤**:
1. 检查控制台错误日志
2. 检查网络面板中的请求状态
3. 验证认证token是否有效

### 问题2: 显示"Failed to load projects"
**可能原因**:
- 后端API错误
- 认证失败
- 数据格式问题

**调试步骤**:
1. 检查后端日志
2. 验证API接口是否正常工作
3. 检查认证头格式

### 问题3: 显示"Your projects will appear here"但实际有项目
**可能原因**:
- API返回空数组
- 数据过滤问题
- 用户权限问题

**调试步骤**:
```javascript
// 在浏览器控制台检查
console.log('Projects data:', projects);
console.log('Projects length:', projects?.length);
```

### 问题4: 项目显示但点击无法打开
**可能原因**:
- 路由配置问题
- 项目ID格式问题
- 编辑器页面问题

**调试步骤**:
1. 检查点击事件是否触发
2. 验证路由跳转URL
3. 检查编辑器页面是否正常

## 🔍 手动测试API

如果项目列表仍然不显示，可以手动测试API：

### 1. 直接访问API
```bash
# 获取认证token
token="你的JWT_TOKEN"

# 测试项目列表API
curl -H "token: $token" http://localhost:8000/api/v1/canva/projects/
```

### 2. 在浏览器控制台测试
```javascript
// 获取当前存储的token
const token = localStorage.getItem('canva_access_token');
console.log('Token:', token);

// 手动调用API
fetch('http://localhost:8000/api/v1/canva/projects/', {
  headers: {
    'token': token,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log('API Response:', data))
.catch(error => console.error('API Error:', error));
```

## 🎯 预期结果

修复成功后，你应该看到：

### 1. **有项目时**
- 项目以网格形式显示
- 每个项目显示标题和更新时间
- 点击项目可以打开编辑器
- 鼠标悬停显示更多操作菜单

### 2. **无项目时**
- 显示空状态提示
- 引导用户创建新项目

### 3. **加载状态**
- 显示加载动画
- 友好的加载提示

## 📋 后续功能

项目列表正常显示后，可以继续实现：

1. **项目操作**: 复制、删除、重命名
2. **项目搜索**: 按标题搜索项目
3. **项目筛选**: 按状态、日期筛选
4. **项目排序**: 按更新时间、创建时间排序
5. **批量操作**: 批量删除、移动项目

让我们开始调试项目列表显示问题！📋✨
