# 形状蒙版和圆角功能实现

## 功能1：形状蒙版功能

### 实现内容
- 在图层管理侧边栏中添加了右键菜单选项"设置为形状蒙版"和"取消形状蒙版"
- 支持将任意图片、形状和文字设置为形状蒙版
- 蒙版图层会影响其上方的所有图层
- 蒙版图层在图层列表中会显示蓝色标识和"(蒙版)"后缀

### 使用方法
1. 在图层管理面板中，右键点击任意图层
2. 选择"设置为形状蒙版"
3. 该图层上方的所有图层将被此形状裁剪
4. 要取消蒙版，右键点击蒙版图层，选择"取消形状蒙版"

### 技术实现
- 使用Fabric.js的`clipPath`属性实现蒙版效果
- 在图层对象上添加`isShapeMask`标识
- 自动应用蒙版到上方图层
- 图层列表实时更新显示蒙版状态

## 功能2：圆角支持

### 实现内容
- 为矩形形状添加了圆角控制功能
- 新增圆角侧边栏，提供滑块和预设值控制
- 在工具栏中添加圆角按钮（仅在选中矩形时显示）
- 支持实时调整圆角半径（0-100px）

### 使用方法
1. 创建或选择一个矩形形状
2. 点击工具栏中的圆角按钮
3. 在圆角侧边栏中：
   - 使用滑块调整圆角半径
   - 点击预设值快速设置
   - 点击预览按钮查看效果

### 技术实现
- 使用Fabric.js的`rx`和`ry`属性控制矩形圆角
- 新增`BorderRadiusSidebar`组件
- 在编辑器中添加`changeBorderRadius`和`getActiveBorderRadius`方法
- 支持分组中的矩形批量设置圆角

## 文件修改列表

### 新增文件
- `src/features/editor/components/border-radius-sidebar.tsx` - 圆角控制侧边栏

### 修改文件
- `src/features/editor/components/layers-sidebar.tsx` - 添加形状蒙版功能
- `src/features/editor/components/toolbar.tsx` - 添加圆角按钮
- `src/features/editor/components/editor.tsx` - 集成圆角侧边栏
- `src/features/editor/hooks/use-editor.ts` - 添加圆角控制方法
- `src/features/editor/types.ts` - 添加类型定义

## 功能特点

### 形状蒙版
- ✅ 支持图片、形状、文字作为蒙版
- ✅ 蒙版影响上方图层
- ✅ 可视化蒙版标识
- ✅ 右键菜单操作
- ✅ 实时预览效果

### 圆角功能
- ✅ 矩形形状圆角支持
- ✅ 0-100px圆角范围
- ✅ 滑块和预设值控制
- ✅ 实时预览
- ✅ 分组支持
- ✅ 工具栏集成

## 使用示例

### 创建圆角矩形
1. 点击形状工具 → 选择矩形
2. 在画布上绘制矩形
3. 选中矩形后，点击工具栏圆角按钮
4. 调整圆角半径到所需值

### 创建形状蒙版
1. 创建一个形状（如星形）
2. 添加一张图片到画布
3. 确保图片在形状上方
4. 右键点击形状 → "设置为形状蒙版"
5. 图片将被裁剪为星形

这两个功能大大增强了编辑器的设计能力，使用户能够创建更丰富的视觉效果。
