# 图层管理功能测试指南

## 测试前准备

1. 确保应用程序正在运行：`npm run dev`
2. 在浏览器中打开 `http://localhost:3001`
3. 创建或打开一个项目
4. 添加一些元素到画布（文本、形状、图片等）

## 功能测试清单

### 1. 基础图层显示
- [ ] 打开图层面板（点击左侧工具栏的图层图标）
- [ ] 验证所有画布元素都显示在图层列表中
- [ ] 检查图层名称是否正确显示
- [ ] 验证图层图标是否正确（文本、图片、形状）

### 2. 图层选择功能
- [ ] 点击图层项，验证对应的画布元素被选中
- [ ] 验证选中的图层在列表中高亮显示
- [ ] 在画布中选择元素，验证对应图层在列表中高亮

### 3. 右键菜单功能
- [ ] 右键点击图层项，验证上下文菜单出现
- [ ] 测试"复制"功能 - 验证图层被复制
- [ ] 测试"重命名"功能 - 验证可以修改图层名称
- [ ] 测试"移至顶层"功能 - 验证图层移动到最上层
- [ ] 测试"向上移动一层"功能 - 验证图层向上移动
- [ ] 测试"向下移动一层"功能 - 验证图层向下移动
- [ ] 测试"移至底层"功能 - 验证图层移动到最下层
- [ ] 测试"显示/隐藏"功能 - 验证图层可见性切换
- [ ] 测试"锁定/解锁"功能 - 验证图层锁定状态切换
- [ ] 测试"删除"功能 - 验证图层被删除

### 4. 拖拽重排序功能
- [ ] 拖拽图层项到不同位置
- [ ] 验证拖拽时的视觉反馈（半透明效果）
- [ ] 验证拖拽目标位置的高亮效果
- [ ] 验证拖拽完成后图层顺序在画布中正确更新
- [ ] 验证拖拽手柄图标显示正确

### 5. 图层状态管理
- [ ] 测试眼睛图标点击 - 切换可见性
- [ ] 测试锁定图标点击 - 切换锁定状态
- [ ] 验证隐藏的图层在画布中不可见
- [ ] 验证锁定的图层在画布中不可选择

### 6. 分组功能
- [ ] 选择多个图层
- [ ] 点击"创建分组"按钮或使用右键菜单
- [ ] 验证分组创建成功
- [ ] 测试分组的展开/折叠功能
- [ ] 测试取消分组功能

### 7. 键盘快捷键
- [ ] Ctrl+D - 复制图层
- [ ] F2 - 重命名图层
- [ ] Ctrl+] - 移至顶层
- [ ] Ctrl+Shift+] - 向上移动一层
- [ ] Ctrl+Shift+[ - 向下移动一层
- [ ] Ctrl+[ - 移至底层
- [ ] Ctrl+; - 显示/隐藏
- [ ] Ctrl+L - 锁定/解锁
- [ ] Delete - 删除图层
- [ ] Ctrl+G - 创建分组
- [ ] Ctrl+Shift+G - 取消分组

## 常见问题排查

### 如果图层列表为空
1. 检查画布中是否有元素
2. 确认元素不是工作区背景
3. 刷新页面重试

### 如果右键菜单不出现
1. 确认是否正确右键点击图层项
2. 检查浏览器控制台是否有错误
3. 尝试刷新页面

### 如果拖拽不工作
1. 确认是否点击了拖拽手柄图标
2. 检查是否有JavaScript错误
3. 尝试在不同浏览器中测试

### 如果图层操作不生效
1. 检查画布中的元素是否正确更新
2. 查看浏览器控制台的错误信息
3. 确认操作的图层确实存在

## 性能测试

### 大量图层测试
1. 创建20+个图层
2. 测试滚动性能
3. 测试拖拽性能
4. 测试右键菜单响应速度

### 复杂分组测试
1. 创建嵌套分组
2. 测试展开/折叠性能
3. 测试分组内拖拽

## 浏览器兼容性测试

测试以下浏览器：
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (如果在Mac上)
- [ ] Edge (最新版本)

## 报告问题

如果发现问题，请记录：
1. 具体的操作步骤
2. 期望的结果
3. 实际的结果
4. 浏览器和版本信息
5. 控制台错误信息（如果有）
