# 🔧 Token同步修复方案

## ✅ 问题诊断

### 🔍 根本问题
调试面板显示没有任何token存储，原因是：

1. **NextAuth在服务器端运行**: `authorize`函数在服务器端执行
2. **localStorage不可访问**: 服务器端无法访问浏览器的localStorage
3. **Token丢失**: 虽然调用了`authService.login`，但token存储在服务器端的localStorage中

### 📊 问题流程
```
1. 用户登录 ✅
2. NextAuth在服务器端调用authService.login ✅
3. Token存储到服务器端localStorage ❌ (浏览器无法访问)
4. 浏览器端localStorage为空 ❌
5. 项目创建时找不到token ❌
```

## 🔧 修复方案

### 1. **NextAuth会话存储Token**
修改了`auth.config.ts`，将token信息保存到NextAuth会话中：

```typescript
// 在authorize函数中
const returnUser = {
  id: userInfo.id,
  email: userInfo.email,
  name: userInfo.name,
  username: userInfo.username,
  accessToken: tokenResponse.access_token,  // 新增
  refreshToken: tokenResponse.refresh_token, // 新增
};
```

### 2. **NextAuth Callbacks处理Token**
更新了callbacks来处理token传递：

```typescript
callbacks: {
  jwt({ token, user }) {
    if (user && (user as any).accessToken) {
      token.accessToken = (user as any).accessToken;
      token.refreshToken = (user as any).refreshToken;
      token.tokenExpires = Date.now() + 24 * 60 * 60 * 1000;
    }
    return token;
  },
  session({ session, token }) {
    if (token.accessToken) {
      (session as any).accessToken = token.accessToken;
      (session as any).refreshToken = token.refreshToken;
      (session as any).tokenExpires = token.tokenExpires;
    }
    return session;
  },
}
```

### 3. **客户端Token同步组件**
创建了`TokenSync`组件，在客户端将NextAuth会话中的token同步到localStorage：

```typescript
// 监听NextAuth会话变化
useEffect(() => {
  if (status === 'authenticated' && session) {
    const sessionWithTokens = session as any;
    if (sessionWithTokens.accessToken) {
      // 同步到localStorage
      localStorage.setItem('canva_access_token', sessionWithTokens.accessToken);
      localStorage.setItem('canva_refresh_token', sessionWithTokens.refreshToken);
      localStorage.setItem('canva_token_expires', sessionWithTokens.tokenExpires.toString());
    }
  }
}, [session, status]);
```

## 🧪 测试步骤

### 1. 清除所有缓存
```javascript
// 在浏览器控制台运行
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### 2. 重新登录
1. 访问: http://localhost:3000/sign-in
2. 输入: `demo` / `demo123`
3. 观察控制台日志

#### 预期登录日志
```
🔐 Attempting login for: demo
✅ Login successful
🎫 Token received from FastAPI backend
💾 Saving tokens to NextAuth JWT: { hasAccessToken: true, hasRefreshToken: true }
🔄 Adding tokens to session
✅ User authenticated, syncing tokens
💾 Syncing tokens from NextAuth session to localStorage
✅ Token sync completed: { hasAccessToken: true, accessTokenLength: 200, ... }
🔍 Storage verification: { tokenStored: true, storedLength: 200, matches: true }
```

### 3. 检查调试面板
登录成功后，调试面板应该显示：
```
Auth Debug Info
Access Token: ✅ (200+ chars)
Refresh Token: ✅ (200+ chars)
Expires: 2025-08-13T...
Is Expired: ✅
Auth Status: ✅
Preview: eyJ0eXAiOiJKV1QiLCJhbGciOi...
```

### 4. 测试项目创建
1. 点击 "Start creating" 按钮
2. 观察详细的认证检查日志

#### 预期项目创建日志
```
🔍 Checking auth status before creating project...
🔍 Detailed auth check: {
  hasAccessToken: true,
  accessTokenLength: 200,
  accessTokenPreview: "eyJ0eXAiOiJKV1QiLCJhbGciOi...",
  hasRefreshToken: true,
  isExpired: false,
  allKeys: ["canva_access_token", "canva_refresh_token", "canva_token_expires"]
}
✅ Auth status OK, proceeding with project creation
📊 Project data to create: { title: "Untitled project", ... }
🔄 POST Request: http://localhost:8000/api/v1/canva/projects/
✅ API Response: { status: 200, ... }
🆕 Creating new project: Untitled project
✅ Project created successfully
```

## 🎯 成功标准

### ✅ 登录阶段
- [ ] NextAuth会话包含token信息
- [ ] TokenSync组件成功同步token到localStorage
- [ ] 调试面板显示所有token状态为✅

### ✅ 项目创建阶段
- [ ] 认证检查显示token存在
- [ ] API调用包含正确的认证头
- [ ] 返回200状态码而不是422
- [ ] 项目创建成功并跳转到编辑器

## 🚨 故障排除

### 问题1: 登录成功但调试面板仍显示❌
**检查步骤**:
1. 查看控制台是否有TokenSync相关日志
2. 检查NextAuth会话是否包含token信息
3. 验证TokenSync组件是否正确加载

### 问题2: Token同步失败
**检查步骤**:
```javascript
// 在浏览器控制台检查NextAuth会话
import { getSession } from 'next-auth/react';
const session = await getSession();
console.log('NextAuth session:', session);
```

### 问题3: 仍然有422错误
**检查步骤**:
1. 确认token正确存储到localStorage
2. 检查网络面板中的请求头
3. 验证后端JWT验证过程

## 🔄 数据流程

修复后的完整认证流程：

```
1. 用户登录 → NextAuth服务器端
2. 调用authService.login → 获取JWT token
3. Token保存到NextAuth会话 → JWT & Session callbacks
4. 客户端TokenSync组件 → 监听会话变化
5. 同步token到localStorage → 浏览器端存储
6. 项目创建时读取token → API调用成功
```

## 🎉 预期结果

修复成功后：

1. **完整的token流程**: 从FastAPI → NextAuth → localStorage
2. **实时同步**: 登录后立即可用
3. **持久化存储**: 页面刷新后token仍然存在
4. **正常API调用**: 422错误消失，项目创建成功
5. **完整功能**: 编辑器、自动保存等功能正常

让我们测试这个完整的修复方案！🔧✨
