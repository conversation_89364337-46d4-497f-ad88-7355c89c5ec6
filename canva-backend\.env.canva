# Canva Backend Environment Configuration

# 应用基础配置
APP_NAME=Canva Clone Backend
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite://./canva.db
# 生产环境使用PostgreSQL
# DATABASE_URL=postgresql://user:password@localhost:5432/canva_db

# JWT认证配置
SECRET_KEY=canva-super-secret-key-change-this-in-production-2024
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS配置 - 允许前端访问
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"]
CORS_ALLOW_HEADERS=["*"]

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,svg,pdf,mp4,mov,avi

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/canva.log

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 安全配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=60

# IP白名单 (开发环境可以留空)
IP_WHITELIST=[]

# 云存储配置 (可选)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_BUCKET_NAME=canva-files
# AWS_REGION=us-east-1

# 阿里云OSS配置 (可选)
# ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key
# ALIYUN_ACCESS_KEY_SECRET=your-aliyun-secret-key
# ALIYUN_BUCKET_NAME=canva-files
# ALIYUN_ENDPOINT=oss-cn-hangzhou.aliyuncs.com

# 邮件配置 (可选)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_TLS=true

# 第三方服务配置 (可选)
# UNSPLASH_ACCESS_KEY=your-unsplash-access-key
# OPENAI_API_KEY=your-openai-api-key
