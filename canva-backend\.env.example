# ==============================================
# React FastAPI Admin 环境配置示例文件
# ==============================================
# 复制此文件为 .env 并根据您的环境修改相应的值

# ==============================================
# 基础应用配置
# ==============================================
APP_ENV=development
# 应用版本
VERSION=0.1.0
# 应用标题
APP_TITLE=React FastAPI Admin
# 项目名称
PROJECT_NAME=React FastAPI Admin
# 应用描述
APP_DESCRIPTION=基于 FastAPI + React + Ant Design 的现代化管理系统
# 调试模式（true/false）
DEBUG=true

# ==============================================
# 安全配置
# ==============================================
# 应用密钥（生产环境请务必修改！使用 openssl rand -hex 32 生成）
SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
# JWT 算法
JWT_ALGORITHM=HS256
# JWT 访问令牌过期时间（分钟）
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=10080
# JWT 刷新令牌过期时间（天）
JWT_REFRESH_TOKEN_EXPIRE_DAYS=30
# JWT 受众
JWT_AUDIENCE=react-fastapi-admin
# JWT 签发者
JWT_ISSUER=react-fastapi-admin

# ==============================================
# IP 白名单配置
# ==============================================
# IP 白名单（用逗号分隔，为空则不启用白名单检查）
# 示例: IP_WHITELIST=*************,********,127.0.0.1
IP_WHITELIST=

# ==============================================
# 请求频率限制配置
# ==============================================
# 是否启用请求频率限制（true/false）
RATE_LIMIT_ENABLED=true
# 时间窗口内最大请求数
RATE_LIMIT_MAX_REQUESTS=60
# 时间窗口大小（秒）
RATE_LIMIT_WINDOW_SECONDS=60

# ==============================================
# 密码策略配置
# ==============================================
# 密码最小长度
PASSWORD_MIN_LENGTH=8
# 是否要求包含大写字母（true/false）
PASSWORD_REQUIRE_UPPERCASE=true
# 是否要求包含小写字母（true/false）
PASSWORD_REQUIRE_LOWERCASE=true
# 是否要求包含数字（true/false）
PASSWORD_REQUIRE_DIGITS=true
# 是否要求包含特殊字符（true/false）
PASSWORD_REQUIRE_SPECIAL=true

# ==============================================
# 数据库配置
# ==============================================
# 数据库连接类型（sqlite/mysql/postgres）
DB_CONNECTION=sqlite
# SQLite 数据库文件名（当 DB_CONNECTION=sqlite 时使用）
DB_FILE=db.sqlite3

# MySQL/PostgreSQL 数据库配置（当 DB_CONNECTION=mysql/postgres 时使用）
# DB_HOST=localhost
# DB_PORT=3306
# DB_USERNAME=root
# DB_PASSWORD=
# DB_DATABASE=fastapi_admin

# ==============================================
# Redis 配置
# ==============================================
# 是否启用 Redis（true/false）
REDIS_ENABLE=true
# Redis 主机地址
REDIS_HOST=localhost
# Redis 端口
REDIS_PORT=6379
# Redis 密码（可选）
REDIS_PASSWORD=
# Redis 数据库索引
REDIS_DB=0
# Redis 键前缀
REDIS_PREFIX=rbac:
# 权限缓存过期时间（秒）
PERMISSION_CACHE_TTL=3600
# 是否启用权限冲突检查（true/false）
PERMISSION_CONFLICT_CHECK=true

# ==============================================
# 阿里云 OSS 存储配置
# ==============================================
# 是否启用 OSS 存储（true/false）
OSS_ENABLED=false
# OSS 访问密钥 ID
OSS_ACCESS_KEY_ID=your_access_key_id
# OSS 访问密钥
OSS_ACCESS_KEY_SECRET=your_access_key_secret
# OSS 存储桶名称
OSS_BUCKET_NAME=your_bucket_name
# OSS 端点（例如：oss-cn-hangzhou.aliyuncs.com）
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
# OSS 自定义域名（可选）
OSS_BUCKET_DOMAIN=
# OSS 上传目录
OSS_UPLOAD_DIR=uploads
# OSS 签名 URL 过期时间（秒）
OSS_URL_EXPIRE_SECONDS=86400

# ==============================================
# 本地存储配置
# ==============================================
# 本地存储 URL 前缀
LOCAL_STORAGE_URL_PREFIX=/static/uploads
# 本地存储完整 URL（可选，例如：http://localhost:8000/static/uploads）
LOCAL_STORAGE_FULL_URL=

# ==============================================
# 开发环境配置示例
# ==============================================
# 如果您在开发环境中，可以使用以下配置：
# APP_ENV=development
# DEBUG=true
# DB_CONNECTION=sqlite
# OSS_ENABLED=false

# ==============================================
# 生产环境配置示例
# ==============================================
# 如果您在生产环境中，请参考以下配置：
# APP_ENV=production
# DEBUG=false
# SECRET_KEY=your_production_secret_key
# DB_CONNECTION=mysql
# DB_HOST=your_database_host
# DB_PASSWORD=your_secure_password
# OSS_ENABLED=true
# OSS_ACCESS_KEY_ID=your_real_access_key
# OSS_ACCESS_KEY_SECRET=your_real_secret_key
# IP_WHITELIST=your_server_ip,your_office_ip

# ==============================================
# 注意事项
# ==============================================
# 1. 生产环境请务必修改 SECRET_KEY
# 2. 数据库密码请使用强密码
# 3. OSS 配置请填写真实的阿里云配置
# 4. IP 白名单建议在生产环境中启用
# 5. 请勿将 .env 文件提交到版本控制系统