# ==============================================================================
# AI工具集
# ==============================================================================
.augment/
.cursor/
.claude/
CLAUDE.md

# ==============================================================================
# 环境配置文件
# ==============================================================================
.env
.env.local
.env.development
.env.production
.env.testing

# ==============================================================================
# 开发工具配置
# ==============================================================================
.vscode/
.idea/
.DS_Store
._.DS_Store

# ==============================================================================
# Python 相关
# ==============================================================================
# 缓存文件
__pycache__/
*.py[cod]
*$py.class
.mypy_cache/
.ruff_cache/
.pytest_cache/

# 虚拟环境
venv/
.venv/
env/
ENV/
env.bak/
venv.bak/
.conda/

# 包管理
pip-log.txt
pip-delete-this-directory.txt
.installed.cfg
*.egg
*.egg-info/
MANIFEST

# 数据库
db.sqlite3
db.sqlite3-journal
db.sqlite3-shm
db.sqlite3-wal

# 日志文件
logs/
*.log

# 测试和覆盖率
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Tortoise 迁移
migrations/
migrations/*

# ==============================================================================
# Node.js / React 相关
# ==============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 构建输出
dist/
build/
.output/

# 缓存
.nuxt/
.vite/
.rollup.cache/
.turbo/

# 编辑器生成的文件
*.swp
*.swo
*~

# ==============================================================================
# 项目特定文件
# ==============================================================================
# 上传和存储
storage/
uploads/
exports/



# 文档构建
docs/_build/
site/

# 备份文件
backups/
*.bak
*.backup
*.orig


# ==============================================================================
# 部署和容器相关
# ==============================================================================
# Docker
Dockerfile.prod
docker-compose.override.yml
.dockerignore

# 云服务
.terraform/
.terraformrc
terraform.tfstate*

# 数据库容器
db_docker/
minio/

# ==============================================================================
# 代码质量工具
# ==============================================================================
# SonarQube
.sonarqube/
.scannerwork/
sonarqube/
sonarqube/extensions/
sonarqube/data/
sonarqube/logs/

# ESLint
.eslintcache

# ==============================================================================
# 操作系统相关
# ==============================================================================
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*

# Linux
*~
.directory
.Trash-*

# ==============================================================================
# 临时文件
# ==============================================================================
*.tmp
*.temp
*.pid
*.seed
*.pid.lock

# ==============================================================================
# 压缩文件
# ==============================================================================
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# ==============================================================================
# 保持空目录的文件
# ==============================================================================
!.gitkeep
