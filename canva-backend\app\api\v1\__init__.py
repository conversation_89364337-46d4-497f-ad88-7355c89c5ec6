from fastapi import APIRouter

from app.core.dependency import Depend<PERSON>er<PERSON>son

from .apis import apis_router
from .auditlog import auditlog_router
from .base import base_router
from .depts import depts_router
from .menus import menus_router
from .roles import roles_router
from .users import users_router

from .upload import upload_router
from .canva_projects import router as canva_projects_router

v1_router = APIRouter()

v1_router.include_router(base_router, prefix="/base")
v1_router.include_router(users_router, prefix="/user", dependencies=[DependPermisson])
v1_router.include_router(roles_router, prefix="/role", dependencies=[DependPermisson])
v1_router.include_router(menus_router, prefix="/menu", dependencies=[DependPermisson])
v1_router.include_router(apis_router, prefix="/api", dependencies=[DependPermisson])
v1_router.include_router(depts_router, prefix="/dept", dependencies=[DependPermisson])
v1_router.include_router(auditlog_router, prefix="/auditlog", dependencies=[DependPermisson])

v1_router.include_router(upload_router, prefix="/upload", dependencies=[DependPermisson])

# Canva相关路由 - 需要认证但不需要权限检查
v1_router.include_router(canva_projects_router, prefix="/canva/projects", tags=["Canva项目"])
