"""
Canva项目API路由
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, Query, status
from fastapi.responses import JSONResponse

from app.controllers.canva_project import project_controller
from app.core.dependency import AuthControl
from app.models.admin import User
from app.models.canva import ProjectStatus
from app.schemas.canva_project import (
    ProjectCreate,
    ProjectDuplicateRequest,
    ProjectListResponse,
    ProjectResponse,
    ProjectSaveRequest,
    ProjectSearchRequest,
    ProjectStatsResponse,
    ProjectUpdate,
)

router = APIRouter()


@router.get("/", response_model=List[ProjectListResponse], summary="获取项目列表")
async def get_projects(
    status: Optional[ProjectStatus] = Query(None, description="项目状态筛选"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=100, description="限制数量"),
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取当前用户的项目列表"""
    projects = await project_controller.get_user_projects(
        user_id=current_user.id,
        status=status,
        skip=skip,
        limit=limit
    )
    return [ProjectListResponse.model_validate(project) for project in projects]


@router.get("/public", response_model=List[ProjectListResponse], summary="获取公开项目")
async def get_public_projects(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=100, description="限制数量")
):
    """获取公开项目列表"""
    projects = await project_controller.get_public_projects(skip=skip, limit=limit)
    return [ProjectListResponse.model_validate(project) for project in projects]


@router.post("/", response_model=ProjectResponse, summary="创建新项目")
async def create_project(
    project_data: ProjectCreate,
    current_user: User = Depends(AuthControl.is_authed)
):
    """创建新项目"""
    project = await project_controller.create_project(project_data, current_user.id)
    return ProjectResponse.model_validate(project)


@router.get("/{project_id}", response_model=ProjectResponse, summary="获取项目详情")
async def get_project(
    project_id: int,
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取项目详情"""
    project = await project_controller.get_user_project(project_id, current_user.id)

    # 增加查看次数
    await project_controller.increment_view_count(project_id)

    return ProjectResponse.model_validate(project)


@router.put("/{project_id}", response_model=ProjectResponse, summary="更新项目")
async def update_project(
    project_id: int,
    project_data: ProjectUpdate,
    current_user: User = Depends(AuthControl.is_authed)
):
    """更新项目"""
    project = await project_controller.update_project(
        project_id, project_data, current_user.id
    )
    return ProjectResponse.model_validate(project)


@router.delete("/{project_id}", summary="删除项目")
async def delete_project(
    project_id: int,
    current_user: User = Depends(AuthControl.is_authed)
):
    """删除项目"""
    await project_controller.delete_project(project_id, current_user.id)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content={"message": "项目删除成功"}
    )


@router.post("/{project_id}/save", response_model=ProjectResponse, summary="保存项目画布")
async def save_project_canvas(
    project_id: int,
    save_data: ProjectSaveRequest,
    current_user: User = Depends(AuthControl.is_authed)
):
    """保存项目画布数据（自动保存功能）"""
    project = await project_controller.save_project_canvas(
        project_id, save_data.canvas_data, current_user.id
    )
    return ProjectResponse.model_validate(project)


@router.post("/{project_id}/duplicate", response_model=ProjectResponse, summary="复制项目")
async def duplicate_project(
    project_id: int,
    duplicate_data: ProjectDuplicateRequest,
    current_user: User = Depends(AuthControl.is_authed)
):
    """复制项目"""
    project = await project_controller.duplicate_project(
        project_id, current_user.id, duplicate_data.title
    )
    return ProjectResponse.model_validate(project)


@router.post("/{project_id}/publish", response_model=ProjectResponse, summary="发布项目")
async def publish_project(
    project_id: int,
    current_user: User = Depends(AuthControl.is_authed)
):
    """发布项目"""
    project = await project_controller.publish_project(project_id, current_user.id)
    return ProjectResponse.model_validate(project)


@router.post("/{project_id}/archive", response_model=ProjectResponse, summary="归档项目")
async def archive_project(
    project_id: int,
    current_user: User = Depends(AuthControl.is_authed)
):
    """归档项目"""
    project = await project_controller.archive_project(project_id, current_user.id)
    return ProjectResponse.model_validate(project)


@router.post("/search", response_model=List[ProjectListResponse], summary="搜索项目")
async def search_projects(
    search_data: ProjectSearchRequest,
    current_user: User = Depends(AuthControl.is_authed)
):
    """搜索项目"""
    projects = await project_controller.search_projects(
        query=search_data.query,
        user_id=current_user.id if not search_data.is_public_only else None,
        is_public_only=search_data.is_public_only,
        skip=search_data.skip,
        limit=search_data.limit
    )
    return [ProjectListResponse.model_validate(project) for project in projects]


@router.get("/stats/overview", response_model=ProjectStatsResponse, summary="获取项目统计")
async def get_project_stats(
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取用户项目统计信息"""
    user_projects = await project_controller.get_user_projects(
        user_id=current_user.id,
        skip=0,
        limit=1000  # 获取所有项目用于统计
    )
    
    total_projects = len(user_projects)
    draft_projects = len([p for p in user_projects if p.status == ProjectStatus.DRAFT])
    published_projects = len([p for p in user_projects if p.status == ProjectStatus.PUBLISHED])
    archived_projects = len([p for p in user_projects if p.status == ProjectStatus.ARCHIVED])
    total_views = sum(p.view_count for p in user_projects)
    total_likes = sum(p.like_count for p in user_projects)
    
    return ProjectStatsResponse(
        total_projects=total_projects,
        draft_projects=draft_projects,
        published_projects=published_projects,
        archived_projects=archived_projects,
        total_views=total_views,
        total_likes=total_likes
    )
