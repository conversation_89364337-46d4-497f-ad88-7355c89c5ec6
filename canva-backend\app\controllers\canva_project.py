"""
Canva项目控制器
"""
from typing import List, Optional
from tortoise.exceptions import DoesNotExist
from fastapi import HTTPException, status

from app.core.crud import CRUDBase
from app.models.canva import Project, ProjectStatus
from app.schemas.canva_project import ProjectCreate, ProjectUpdate


class ProjectController(CRUDBase[Project, ProjectCreate, ProjectUpdate]):
    def __init__(self):
        super().__init__(model=Project)

    async def get_user_projects(
        self, 
        user_id: int, 
        status: Optional[ProjectStatus] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[Project]:
        """获取用户的项目列表"""
        query = self.model.filter(user_id=user_id)
        
        if status:
            query = query.filter(status=status)
            
        return await query.offset(skip).limit(limit).order_by("-updated_at")

    async def get_public_projects(
        self, 
        skip: int = 0,
        limit: int = 20
    ) -> List[Project]:
        """获取公开项目列表"""
        return await self.model.filter(
            is_public=True,
            status=ProjectStatus.PUBLISHED
        ).offset(skip).limit(limit).order_by("-updated_at")

    async def create_project(self, obj_in: ProjectCreate, user_id: int) -> Project:
        """创建项目"""
        project_data = obj_in.model_dump()
        project_data["user_id"] = user_id
        
        # 如果没有提供画布数据，创建默认的空画布
        if not project_data.get("canvas_data"):
            project_data["canvas_data"] = {
                "version": "5.3.0",
                "objects": [],
                "background": "#ffffff"
            }
        
        project = await self.model.create(**project_data)
        return project

    async def update_project(
        self, 
        project_id: int, 
        obj_in: ProjectUpdate, 
        user_id: int
    ) -> Project:
        """更新项目"""
        project = await self.get_user_project(project_id, user_id)
        
        update_data = obj_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(project, field, value)
        
        await project.save()
        return project

    async def get_user_project(self, project_id: int, user_id: int) -> Project:
        """获取用户的特定项目"""
        try:
            project = await self.model.get(id=project_id, user_id=user_id)
            return project
        except DoesNotExist:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在或无权访问"
            )

    async def delete_project(self, project_id: int, user_id: int) -> bool:
        """删除项目"""
        project = await self.get_user_project(project_id, user_id)
        await project.delete()
        return True

    async def save_project_canvas(
        self, 
        project_id: int, 
        canvas_data: dict, 
        user_id: int
    ) -> Project:
        """保存项目画布数据"""
        project = await self.get_user_project(project_id, user_id)
        project.canvas_data = canvas_data
        await project.save()
        return project

    async def duplicate_project(
        self, 
        project_id: int, 
        user_id: int,
        new_title: Optional[str] = None
    ) -> Project:
        """复制项目"""
        original_project = await self.get_user_project(project_id, user_id)
        
        # 创建新项目数据
        new_project_data = {
            "title": new_title or f"{original_project.title} (副本)",
            "description": original_project.description,
            "canvas_data": original_project.canvas_data,
            "width": original_project.width,
            "height": original_project.height,
            "user_id": user_id,
            "status": ProjectStatus.DRAFT,
            "is_public": False,
            "is_template": False
        }
        
        new_project = await self.model.create(**new_project_data)
        return new_project

    async def publish_project(self, project_id: int, user_id: int) -> Project:
        """发布项目"""
        project = await self.get_user_project(project_id, user_id)
        project.status = ProjectStatus.PUBLISHED
        await project.save()
        return project

    async def archive_project(self, project_id: int, user_id: int) -> Project:
        """归档项目"""
        project = await self.get_user_project(project_id, user_id)
        project.status = ProjectStatus.ARCHIVED
        await project.save()
        return project

    async def increment_view_count(self, project_id: int) -> None:
        """增加项目查看次数"""
        try:
            project = await self.model.get(id=project_id)
            project.view_count += 1
            await project.save()
        except DoesNotExist:
            pass  # 项目不存在时忽略

    async def search_projects(
        self,
        query: str,
        user_id: Optional[int] = None,
        is_public_only: bool = False,
        skip: int = 0,
        limit: int = 20
    ) -> List[Project]:
        """搜索项目"""
        filters = {}
        
        if user_id:
            filters["user_id"] = user_id
        
        if is_public_only:
            filters["is_public"] = True
            filters["status"] = ProjectStatus.PUBLISHED
        
        # 在标题和描述中搜索
        projects = await self.model.filter(
            **filters
        ).filter(
            title__icontains=query
        ).offset(skip).limit(limit).order_by("-updated_at")
        
        return projects


# 创建全局实例
project_controller = ProjectController()
