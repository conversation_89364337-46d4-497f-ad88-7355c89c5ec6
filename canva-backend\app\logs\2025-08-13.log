2025-08-13 10:37:53 | INFO     | app.utils.log_control:setup_logger:130 | 日志系统已配置 - 环境: development
2025-08-13 10:37:53 | INFO     | app.utils.log_control:setup_logger:131 | 日志目录: F:\Project\canva-clone-main\canva-backend\app\logs
2025-08-13 10:37:53 | INFO     | app.utils.log_control:setup_logger:132 | 调试模式: True
2025-08-13 10:37:53 | INFO     | app.utils.log_control:setup_logger:133 | 日志保留天数: 7
2025-08-13 10:37:53 | INFO     | app.utils.log_control:init_logging:220 | ==================================================
2025-08-13 10:37:53 | INFO     | app.utils.log_control:init_logging:221 | 🚀 React FastAPI Admin 正在启动...
2025-08-13 10:37:53 | INFO     | app.utils.log_control:init_logging:222 | 📍 环境: development
2025-08-13 10:37:53 | INFO     | app.utils.log_control:init_logging:223 | 🔧 调试模式: True
2025-08-13 10:37:53 | INFO     | app.utils.log_control:init_logging:224 | 📂 项目根目录: F:\Project\canva-clone-main\canva-backend
2025-08-13 10:37:53 | INFO     | app.utils.log_control:init_logging:225 | 📋 日志目录: F:\Project\canva-clone-main\canva-backend\app\logs
2025-08-13 10:37:53 | INFO     | app.utils.log_control:init_logging:226 | ==================================================
2025-08-13 10:37:53 | INFO     | app:lifespan:47 | 正在初始化应用...
2025-08-13 10:37:53 | INFO     | app:lifespan:52 | 身份验证控制器初始化完成
2025-08-13 10:37:57 | INFO     | app.core.init_app:init_permissions_and_assign_roles:243 | 系统中共有 57 个活跃权限
2025-08-13 10:37:57 | INFO     | app.core.init_app:init_permissions_and_assign_roles:247 | 管理员角色当前有 57 个权限
2025-08-13 10:37:57 | INFO     | app.core.init_app:init_permissions_and_assign_roles:254 | 为管理员角色重新分配了 57 个权限
2025-08-13 10:37:57 | INFO     | app.core.init_app:init_permissions_and_assign_roles:269 | 超级管理员用户已经拥有管理员角色
2025-08-13 10:37:57 | INFO     | app:lifespan:56 | 数据初始化完成
2025-08-13 10:41:40 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.827s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:50:12 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:50:42 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:50:46 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.012s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:50:57 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:50:59 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.150s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:51:22 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.025s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:51:23 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.045s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:51:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.060s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:51:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.110s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:51:27 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:51:28 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:51:32 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:05 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:07 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:09 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.024s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:17 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:52:38 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:53:58 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:53:59 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:03 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:07 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:11 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:16 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:18 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:19 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:20 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:54:29 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:55:38 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:55:54 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.021s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:56:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/2 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:56:05 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.143s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:57:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 10:57:14 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.011s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 11:03:59 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.024s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 11:06:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 11:22:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 11:25:12 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.013s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 11:25:25 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.023s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 11:25:26 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.029s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 11:28:21 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:01:19 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:07:12 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:27:53 | INFO     | app.utils.log_control:setup_logger:130 | 日志系统已配置 - 环境: development
2025-08-13 13:27:53 | INFO     | app.utils.log_control:setup_logger:131 | 日志目录: F:\Project\canva-clone-main\canva-backend\app\logs
2025-08-13 13:27:53 | INFO     | app.utils.log_control:setup_logger:132 | 调试模式: True
2025-08-13 13:27:53 | INFO     | app.utils.log_control:setup_logger:133 | 日志保留天数: 7
2025-08-13 13:27:53 | INFO     | app.utils.log_control:init_logging:220 | ==================================================
2025-08-13 13:27:53 | INFO     | app.utils.log_control:init_logging:221 | 🚀 React FastAPI Admin 正在启动...
2025-08-13 13:27:53 | INFO     | app.utils.log_control:init_logging:222 | 📍 环境: development
2025-08-13 13:27:53 | INFO     | app.utils.log_control:init_logging:223 | 🔧 调试模式: True
2025-08-13 13:27:53 | INFO     | app.utils.log_control:init_logging:224 | 📂 项目根目录: F:\Project\canva-clone-main\canva-backend
2025-08-13 13:27:53 | INFO     | app.utils.log_control:init_logging:225 | 📋 日志目录: F:\Project\canva-clone-main\canva-backend\app\logs
2025-08-13 13:27:53 | INFO     | app.utils.log_control:init_logging:226 | ==================================================
2025-08-13 13:27:53 | INFO     | app:lifespan:47 | 正在初始化应用...
2025-08-13 13:27:53 | INFO     | app:lifespan:52 | 身份验证控制器初始化完成
2025-08-13 13:27:55 | INFO     | app.core.init_app:init_permissions_and_assign_roles:243 | 系统中共有 57 个活跃权限
2025-08-13 13:27:55 | INFO     | app.core.init_app:init_permissions_and_assign_roles:247 | 管理员角色当前有 57 个权限
2025-08-13 13:27:55 | INFO     | app.core.init_app:init_permissions_and_assign_roles:254 | 为管理员角色重新分配了 57 个权限
2025-08-13 13:27:55 | INFO     | app.core.init_app:init_permissions_and_assign_roles:269 | 超级管理员用户已经拥有管理员角色
2025-08-13 13:27:55 | INFO     | app:lifespan:56 | 数据初始化完成
2025-08-13 13:28:28 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.231s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:28:57 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:28:58 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.041s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:06 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.027s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:07 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:09 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:10 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:11 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:11 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:12 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:29:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:31:20 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:32:05 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:32:06 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:32:08 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:35:25 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:36:06 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.023s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:36:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:36:27 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:41:27 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:41:31 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:41:46 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:41:55 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:41:58 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:42:45 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:42:48 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:42:49 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:42:50 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:42:53 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:42:53 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:03 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:07 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:08 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:12 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:13 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:14 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:22 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:23 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:24 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:25 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:26 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:41 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:43 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:43 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:47 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:47 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:53 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:55 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:43:59 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:07 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.018s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:17 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:20 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:20 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:33 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/2 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:34 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:40 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:42 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:47 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:53 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:53 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:56 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:44:56 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:00 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:01 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:02 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:07 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:13 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:17 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:18 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:31 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.014s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:32 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:38 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | POST | http://localhost:8000/api/v1/canva/projects/ | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.025s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:40 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:45:58 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:01 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.029s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:02 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:04 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:05 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:07 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:21 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:26 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:29 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:33 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:36 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:37 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:38 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:39 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:47 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:48 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:48 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:50 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:46:50 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:47:38 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:47:39 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:47:43 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.089s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:47:58 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:48:02 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:48:05 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:48:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/9 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:48:41 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:49:08 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:49:09 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:49:11 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:49:12 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:49:14 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:49:15 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:49:17 | WARNING  | app.utils.log_control:dispatch:186 | HTTP 422 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 13:56:28 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/9 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:00:18 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:00:19 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:00:22 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:00:34 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/8 | 0.041s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:00:34 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/8 | 0.036s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:00:51 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:00:51 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:01:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:01:19 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:01:20 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:01:22 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:01:26 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:01:26 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:01:33 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:06 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:12 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:14 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:19 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:21 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:22 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:26 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:27 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:02:28 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/2 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:03:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | POST | http://localhost:8000/api/v1/canva/projects/ | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:03:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:03:16 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/10 | 0.016s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:03:20 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/10 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:05:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:08:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:11:34 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:11:43 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/10 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:11:47 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/10 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:11:47 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/10 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:11:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:11:59 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/10 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:14:22 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.022s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:17:08 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:19:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:20:13 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:21:18 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/10 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:25:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:25:10 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:26:02 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.012s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:26:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:26:16 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:26 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:35 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:36 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:37 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:38 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:40 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:41 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:42 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:43 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.017s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:45 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:48 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:50 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:27:57 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.013s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:28:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:28:05 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:29:33 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:29:34 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:29:35 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:29:41 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.016s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:29:44 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:29:45 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:29:59 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.014s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:08 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.013s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:09 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:21 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:23 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:25 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:29 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:31 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:33 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:36 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:39 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:40 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:30:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.012s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | POST | http://localhost:8000/api/v1/canva/projects/ | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.011s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:07 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.019s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.013s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:18 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.013s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:19 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:27 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:28 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:30 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:31 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:44 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:47 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:50 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:54 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:31:57 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.014s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:32:01 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:32:07 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:32:08 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:32:09 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:32:11 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:32:12 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:33:47 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.004s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:33:53 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:33:56 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:33:56 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:33:58 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:06 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:06 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:08 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:12 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:14 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:16 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:29 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:31 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:43 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:50 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:34:53 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:35:00 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.014s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:35:04 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:03 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:05 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:08 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:14 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/6 | 0.012s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/6 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:21 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.027s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:25 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/11 | 0.015s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:26 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:37 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:40 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:41 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.017s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:45 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:48 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:40:53 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:41:01 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:41:19 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:41:21 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:41:24 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:17 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/9 | 0.005s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:18 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/9 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:27 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.014s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:31 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | DELETE | http://localhost:8000/api/v1/canva/projects/9 | 0.041s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:31 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:44 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:47 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | DELETE | http://localhost:8000/api/v1/canva/projects/7 | 0.003s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:43:47 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:06 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:16 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/ | 0.013s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:20 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | GET | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:21 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:27 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.016s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:29 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.012s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:30 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:30 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:42 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:44 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:46 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.006s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:46 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:47 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:49 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:55:51 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.020s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:56:00 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.007s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:56:07 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:56:11 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.011s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:56:14 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.010s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:56:16 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.008s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:57:05 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
2025-08-13 14:57:15 | INFO     | app.utils.log_control:dispatch:188 | HTTP 200 | 127.0.0.1 | PUT | http://localhost:8000/api/v1/canva/projects/11 | 0.009s | UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa
