"""
Canva相关数据模型
"""
from enum import Enum
from tortoise import fields

from .base import BaseModel, TimestampMixin


class ProjectStatus(str, Enum):
    """项目状态枚举"""
    DRAFT = "draft"          # 草稿
    PUBLISHED = "published"  # 已发布
    ARCHIVED = "archived"    # 已归档


class FileType(str, Enum):
    """文件类型枚举"""
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    DOCUMENT = "document"
    OTHER = "other"


class Project(BaseModel, TimestampMixin):
    """设计项目模型"""
    
    # 基本信息
    title = fields.CharField(max_length=200, description="项目标题")
    description = fields.TextField(null=True, description="项目描述")
    
    # 画布数据
    canvas_data = fields.JSONField(description="画布数据(JSON格式)")
    thumbnail_url = fields.TextField(null=True, description="缩略图URL")
    
    # 画布尺寸
    width = fields.IntField(default=800, description="画布宽度")
    height = fields.IntField(default=600, description="画布高度")
    
    # 状态信息
    status = fields.CharEnumField(ProjectStatus, default=ProjectStatus.DRAFT, description="项目状态")
    is_template = fields.BooleanField(default=False, description="是否为模板")
    is_public = fields.BooleanField(default=False, description="是否公开")
    
    # 关联关系
    user = fields.ForeignKeyField("models.User", related_name="projects", description="项目所有者")
    
    # 统计信息
    view_count = fields.IntField(default=0, description="查看次数")
    like_count = fields.IntField(default=0, description="点赞次数")
    
    class Meta:
        table = "canva_projects"
        
    def __str__(self):
        return f"Project(title={self.title})"


class ProjectVersion(BaseModel, TimestampMixin):
    """项目版本模型"""
    
    # 版本信息
    version_number = fields.IntField(description="版本号")
    description = fields.CharField(max_length=500, null=True, description="版本描述")
    
    # 版本数据
    canvas_data = fields.JSONField(description="画布数据快照")
    
    # 关联关系
    project = fields.ForeignKeyField("models.Project", related_name="versions", description="所属项目")
    
    class Meta:
        table = "canva_project_versions"
        unique_together = (("project", "version_number"),)
        
    def __str__(self):
        return f"ProjectVersion(project={self.project_id}, version={self.version_number})"


class TemplateCategory(BaseModel, TimestampMixin):
    """模板分类模型"""
    
    name = fields.CharField(max_length=100, unique=True, description="分类名称")
    description = fields.TextField(null=True, description="分类描述")
    icon = fields.CharField(max_length=100, null=True, description="分类图标")
    sort_order = fields.IntField(default=0, description="排序顺序")
    is_active = fields.BooleanField(default=True, description="是否启用")
    
    class Meta:
        table = "canva_template_categories"
        
    def __str__(self):
        return f"TemplateCategory(name={self.name})"


class Template(BaseModel, TimestampMixin):
    """设计模板模型"""
    
    # 基本信息
    title = fields.CharField(max_length=200, description="模板标题")
    description = fields.TextField(null=True, description="模板描述")
    
    # 画布数据
    canvas_data = fields.JSONField(description="画布数据(JSON格式)")
    thumbnail_url = fields.CharField(max_length=500, null=True, description="缩略图URL")
    preview_images = fields.JSONField(default=list, description="预览图片列表")
    
    # 画布尺寸
    width = fields.IntField(default=800, description="画布宽度")
    height = fields.IntField(default=600, description="画布高度")
    
    # 分类和标签
    category = fields.ForeignKeyField("models.TemplateCategory", related_name="templates", null=True, description="模板分类")
    tags = fields.JSONField(default=list, description="标签列表")
    
    # 状态信息
    is_featured = fields.BooleanField(default=False, description="是否精选")
    is_premium = fields.BooleanField(default=False, description="是否高级模板")
    is_public = fields.BooleanField(default=True, description="是否公开")
    
    # 关联关系
    creator = fields.ForeignKeyField("models.User", related_name="created_templates", description="创建者")
    
    # 统计信息
    download_count = fields.IntField(default=0, description="下载次数")
    like_count = fields.IntField(default=0, description="点赞次数")
    view_count = fields.IntField(default=0, description="查看次数")
    
    class Meta:
        table = "canva_templates"
        
    def __str__(self):
        return f"Template(title={self.title})"


class CanvaFile(BaseModel, TimestampMixin):
    """文件模型"""
    
    # 文件信息
    filename = fields.CharField(max_length=255, description="文件名")
    original_name = fields.CharField(max_length=255, description="原始文件名")
    file_path = fields.CharField(max_length=500, description="文件路径")
    file_url = fields.CharField(max_length=500, null=True, description="文件访问URL")
    
    # 文件属性
    file_size = fields.BigIntField(description="文件大小(字节)")
    mime_type = fields.CharField(max_length=100, description="MIME类型")
    file_type = fields.CharEnumField(FileType, description="文件类型")
    
    # 图片特有属性
    width = fields.IntField(null=True, description="图片宽度")
    height = fields.IntField(null=True, description="图片高度")
    
    # 状态信息
    is_public = fields.BooleanField(default=False, description="是否公开")
    is_processed = fields.BooleanField(default=True, description="是否已处理")
    
    # 关联关系
    user = fields.ForeignKeyField("models.User", related_name="canva_files", description="文件所有者")
    
    # 元数据
    metadata = fields.JSONField(default=dict, description="文件元数据")
    
    class Meta:
        table = "canva_files"
        
    def __str__(self):
        return f"CanvaFile(filename={self.filename})"


class ProjectCollaborator(BaseModel, TimestampMixin):
    """项目协作者模型"""
    
    class Permission(str, Enum):
        """权限枚举"""
        VIEW = "view"      # 查看
        EDIT = "edit"      # 编辑
        ADMIN = "admin"    # 管理
    
    # 关联关系
    project = fields.ForeignKeyField("models.Project", related_name="collaborators", description="项目")
    user = fields.ForeignKeyField("models.User", related_name="collaborations", description="协作用户")
    
    # 权限信息
    permission = fields.CharEnumField(Permission, default=Permission.VIEW, description="权限级别")
    
    class Meta:
        table = "canva_project_collaborators"
        unique_together = (("project", "user"),)
        
    def __str__(self):
        return f"ProjectCollaborator(project={self.project_id}, user={self.user_id})"


class TemplateLike(BaseModel, TimestampMixin):
    """模板点赞模型"""
    
    # 关联关系
    template = fields.ForeignKeyField("models.Template", related_name="likes", description="模板")
    user = fields.ForeignKeyField("models.User", related_name="template_likes", description="用户")
    
    class Meta:
        table = "canva_template_likes"
        unique_together = (("template", "user"),)
        
    def __str__(self):
        return f"TemplateLike(template={self.template_id}, user={self.user_id})"
