"""
Canva项目相关Pydantic模式
"""
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator

from app.models.canva import ProjectStatus


class ProjectBase(BaseModel):
    """项目基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="项目标题")
    description: Optional[str] = Field(None, description="项目描述")
    width: int = Field(default=800, ge=100, le=5000, description="画布宽度")
    height: int = Field(default=600, ge=100, le=5000, description="画布高度")
    is_public: bool = Field(default=False, description="是否公开")
    is_template: bool = Field(default=False, description="是否为模板")


class ProjectCreate(ProjectBase):
    """项目创建模式"""
    canvas_data: Optional[Dict[str, Any]] = Field(None, description="画布数据")
    
    @validator('canvas_data')
    def validate_canvas_data(cls, v):
        """验证画布数据格式"""
        if v is not None:
            # 基本的画布数据验证
            if not isinstance(v, dict):
                raise ValueError('画布数据必须是字典格式')
            
            # 检查必要字段
            if 'objects' not in v:
                v['objects'] = []
            if 'background' not in v:
                v['background'] = '#ffffff'
                
        return v


class ProjectUpdate(BaseModel):
    """项目更新模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="项目标题")
    description: Optional[str] = Field(None, description="项目描述")
    canvas_data: Optional[Dict[str, Any]] = Field(None, description="画布数据")
    thumbnail_url: Optional[str] = Field(None, description="缩略图URL")
    width: Optional[int] = Field(None, ge=100, le=5000, description="画布宽度")
    height: Optional[int] = Field(None, ge=100, le=5000, description="画布高度")
    status: Optional[ProjectStatus] = Field(None, description="项目状态")
    is_public: Optional[bool] = Field(None, description="是否公开")
    is_template: Optional[bool] = Field(None, description="是否为模板")


class ProjectResponse(ProjectBase):
    """项目响应模式"""
    id: int
    canvas_data: Dict[str, Any]
    thumbnail_url: Optional[str] = None
    status: ProjectStatus
    user_id: int
    view_count: int = 0
    like_count: int = 0
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProjectListResponse(BaseModel):
    """项目列表响应模式"""
    id: int
    title: str
    description: Optional[str] = None
    thumbnail_url: Optional[str] = None
    width: int
    height: int
    status: ProjectStatus
    is_public: bool
    is_template: bool
    view_count: int = 0
    like_count: int = 0
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ProjectSaveRequest(BaseModel):
    """项目保存请求模式"""
    canvas_data: Dict[str, Any] = Field(..., description="画布数据")
    
    @validator('canvas_data')
    def validate_canvas_data(cls, v):
        """验证画布数据格式"""
        if not isinstance(v, dict):
            raise ValueError('画布数据必须是字典格式')
        return v


class ProjectDuplicateRequest(BaseModel):
    """项目复制请求模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="新项目标题")


class ProjectSearchRequest(BaseModel):
    """项目搜索请求模式"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    is_public_only: bool = Field(default=False, description="仅搜索公开项目")
    skip: int = Field(default=0, ge=0, description="跳过数量")
    limit: int = Field(default=20, ge=1, le=100, description="限制数量")


class ProjectStatsResponse(BaseModel):
    """项目统计响应模式"""
    total_projects: int
    draft_projects: int
    published_projects: int
    archived_projects: int
    total_views: int
    total_likes: int
