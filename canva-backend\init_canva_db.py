"""
Canva数据库初始化脚本
"""
import asyncio
import os
import sys
from pathlib import Path

def setup_environment():
    """设置环境"""
    # 添加项目根目录到Python路径
    project_root = str(Path(__file__).parent)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    # 设置环境变量
    os.environ.setdefault('PYTHONPATH', project_root)

    # 检查.env文件
    env_file = Path(project_root) / '.env'
    if not env_file.exists():
        env_template = Path(project_root) / '.env.canva'
        if env_template.exists():
            import shutil
            shutil.copy(env_template, env_file)
            print("✅ 已复制 .env.canva 到 .env")
        else:
            print("❌ 未找到环境配置文件")
            return False
    return True

# 设置环境
if not setup_environment():
    sys.exit(1)

# 导入必要的模块
try:
    from tortoise import Tortoise
    from app.settings.config import settings
    from app.models.admin import User
    from app.models.canva import TemplateCategory
    from app.utils.password import get_password_hash
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


async def init_database():
    """初始化数据库"""
    print("🔄 正在初始化数据库...")
    
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.tortoise_orm)
    
    # 生成数据库表
    await Tortoise.generate_schemas()
    
    print("✅ 数据库表创建成功")


async def create_superuser():
    """创建超级管理员用户"""
    print("🔄 正在创建超级管理员...")
    
    # 检查是否已存在超级管理员
    existing_admin = await User.filter(is_superuser=True).first()
    if existing_admin:
        print(f"✅ 超级管理员已存在: {existing_admin.username}")
        return existing_admin
    
    # 创建超级管理员
    admin_user = await User.create(
        username="admin",
        nickname="超级管理员",
        email="<EMAIL>",
        password=get_password_hash("admin123"),
        is_active=True,
        is_superuser=True,
        first_name="Admin",
        last_name="User",
        is_premium=True,
        is_verified=True
    )
    
    print(f"✅ 超级管理员创建成功: {admin_user.username}")
    print(f"   用户名: admin")
    print(f"   密码: admin123")
    print(f"   邮箱: <EMAIL>")
    
    return admin_user


async def create_demo_user():
    """创建演示用户"""
    print("🔄 正在创建演示用户...")
    
    # 检查是否已存在演示用户
    existing_demo = await User.filter(username="demo").first()
    if existing_demo:
        print(f"✅ 演示用户已存在: {existing_demo.username}")
        return existing_demo
    
    # 创建演示用户
    demo_user = await User.create(
        username="demo",
        nickname="演示用户",
        email="<EMAIL>",
        password=get_password_hash("demo123"),
        is_active=True,
        is_superuser=False,
        first_name="Demo",
        last_name="User",
        is_premium=False,
        is_verified=True
    )
    
    print(f"✅ 演示用户创建成功: {demo_user.username}")
    print(f"   用户名: demo")
    print(f"   密码: demo123")
    print(f"   邮箱: <EMAIL>")
    
    return demo_user


async def create_template_categories():
    """创建模板分类"""
    print("🔄 正在创建模板分类...")
    
    categories = [
        {"name": "社交媒体", "description": "Instagram、Facebook、Twitter等社交媒体设计", "icon": "share", "sort_order": 1},
        {"name": "演示文稿", "description": "PPT、Keynote等演示文稿模板", "icon": "presentation", "sort_order": 2},
        {"name": "海报传单", "description": "宣传海报、传单、广告设计", "icon": "image", "sort_order": 3},
        {"name": "名片卡片", "description": "商务名片、贺卡、邀请函", "icon": "credit-card", "sort_order": 4},
        {"name": "品牌标识", "description": "Logo、品牌标识设计", "icon": "award", "sort_order": 5},
        {"name": "文档报告", "description": "报告、简历、文档模板", "icon": "file-text", "sort_order": 6},
        {"name": "网页设计", "description": "网页横幅、网站设计元素", "icon": "monitor", "sort_order": 7},
        {"name": "移动应用", "description": "App界面、移动端设计", "icon": "smartphone", "sort_order": 8},
    ]
    
    created_count = 0
    for category_data in categories:
        existing_category = await TemplateCategory.filter(name=category_data["name"]).first()
        if not existing_category:
            await TemplateCategory.create(**category_data)
            created_count += 1
            print(f"   ✅ 创建分类: {category_data['name']}")
        else:
            print(f"   ⏭️ 分类已存在: {category_data['name']}")
    
    print(f"✅ 模板分类创建完成，新增 {created_count} 个分类")


async def create_directories():
    """创建必要的目录"""
    print("🔄 正在创建必要的目录...")
    
    directories = [
        "uploads",
        "uploads/images",
        "uploads/videos",
        "uploads/documents",
        "uploads/avatars",
        "uploads/thumbnails",
        "logs",
        "static",
        "static/templates",
        "static/assets"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ 创建目录: {directory}")
    
    print("✅ 目录创建完成")


async def main():
    """主函数"""
    print("🚀 Canva Backend 数据库初始化")
    print("=" * 50)
    
    try:
        # 创建必要的目录
        await create_directories()
        
        # 初始化数据库
        await init_database()
        
        # 创建超级管理员
        await create_superuser()
        
        # 创建演示用户
        await create_demo_user()
        
        # 创建模板分类
        await create_template_categories()
        
        print("\n" + "=" * 50)
        print("🎉 数据库初始化完成！")
        print("\n📝 接下来的步骤:")
        print("1. 复制 .env.canva 到 .env")
        print("2. 根据需要修改 .env 配置")
        print("3. 启动服务: python main.py")
        print("4. 访问 API 文档: http://localhost:8000/docs")
        print("5. 使用以下账户登录测试:")
        print("   - 管理员: admin / admin123")
        print("   - 演示用户: demo / demo123")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            await Tortoise.close_connections()
        except:
            pass


if __name__ == "__main__":
    # 检查环境配置
    if not os.path.exists(".env"):
        print("⚠️ 未找到 .env 文件")
        print("请先复制 .env.canva 到 .env 并根据需要修改配置")
        print("命令: cp .env.canva .env")
        sys.exit(1)
    
    # 运行初始化
    asyncio.run(main())
