[project]
name = "react-fastapi-admin"
version = "0.1.0"
description = "React Fastapi admin"
requires-python = ">=3.11"
dependencies = [
    # 核心框架
    "fastapi==0.116.1",
    "tortoise-orm==0.25.1",
    "pydantic==2.11.7",
    "pydantic-core==2.33.2",
    "pydantic-settings==2.10.1",
    "starlette==0.47.1",

    # Redis
    "aioredis==2.0.1",
    "async-timeout==5.0.1",

    # 服务器
    "granian==2.4.1",

    # 认证和安全
    "bcrypt==4.3.0",
    "pyjwt==2.10.1",
    "cryptography==45.0.5",

    # 数据库
    "aiosqlite==0.21.0",
    "aerich==0.9.1",

    # 工具库
    "loguru==0.7.3",
    "email-validator==2.2.0",
    "python-dotenv==1.1.1",
    "python-multipart==0.0.20",
    "requests==2.32.4",

    # 阿里云集成
    "aliyun-python-sdk-core==2.16.0",
    "aliyun-python-sdk-kms==2.16.5",
    "oss2==2.19.1",
    "pycryptodome==3.23.0",
    "crcmod==1.7",

    # 类型支持
    "annotated-types==0.7.0",
    "typing-extensions==4.14.1",
    "typing-inspection==0.4.1",

    # 开发工具
    "setuptools==80.9.0",
    "watchfiles==1.1.0",

    # 其他依赖
    "anyio==4.9.0",
    "asyncclick==8.1.8",
    "certifi==2025.7.9",
    "cffi==1.17.1",
    "charset-normalizer==3.4.2",
    "click==8.2.1",
    "colorama==0.4.6",
    "dictdiffer==0.9.0",
    "dnspython==2.7.0",
    "idna==3.10",
    "iso8601==2.1.0",
    "jmespath==0.10.0",
    "pycparser==2.22",
    "pypika-tortoise==0.6.1",
    "pytz==2025.2",
    "six==1.17.0",
    "sniffio==1.3.1",
    "urllib3==2.5.0",
    "atlastk==0.13.5",
    "win32-setctime==1.2.0",
]

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 120
target-version = ["py311"]

[tool.ruff]
line-length = 120
target-version = "py311"

[tool.ruff.lint]
extend-select = []
ignore = ["F403", "F405"]

[tool.isort]
profile = "black"
line_length = 120

[tool.aerich]
tortoise_orm = "app.settings.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."
