# 核心框架
fastapi==0.116.1
tortoise-orm==0.25.1
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1
starlette==0.47.1

# Redis
aioredis==2.0.1
async-timeout==5.0.1

# 服务器
granian==2.4.1
uvicorn==0.32.1

# 认证和安全
bcrypt==4.3.0
pyjwt==2.10.1
cryptography==45.0.5
passlib[bcrypt]==1.7.4

# 数据库
aiosqlite==0.21.0
aerich==0.9.1

# 工具库
loguru==0.7.3
email-validator==2.2.0
python-dotenv==1.1.1
python-multipart==0.0.20
requests==2.32.4

# 文件处理
pillow==11.1.0
aiofiles==24.1.0

# 阿里云集成 (可选)
aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-kms==2.16.5
oss2==2.19.1
pycryptodome==3.23.0
crcmod==1.7

# 类型支持
annotated-types==0.7.0
typing-extensions==4.14.1
typing-inspection==0.4.1

# 其他依赖
anyio==4.9.0
certifi==2025.7.9
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
idna==3.10
sniffio==1.3.1
urllib3==2.5.0
