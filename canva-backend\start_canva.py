"""
Canva Backend 启动脚本
"""
import os
import sys
import subprocess
import asyncio
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("请使用Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    if not os.path.exists(".env"):
        print("❌ 未找到 .env 文件")
        
        if os.path.exists(".env.canva"):
            print("📋 发现 .env.canva 模板文件")
            response = input("是否复制 .env.canva 到 .env? (y/n): ")
            if response.lower() in ['y', 'yes']:
                import shutil
                shutil.copy(".env.canva", ".env")
                print("✅ 已复制 .env.canva 到 .env")
            else:
                print("请手动创建 .env 文件")
                return False
        else:
            print("请创建 .env 文件或复制 .env.canva")
            return False
    
    print("✅ 环境配置文件存在")
    return True

def check_database():
    """检查数据库"""
    print("🔍 检查数据库...")
    
    # 检查SQLite数据库文件
    if os.path.exists("canva.db"):
        print("✅ 数据库文件存在")
        return True
    else:
        print("❌ 数据库文件不存在")
        response = input("是否初始化数据库? (y/n): ")
        if response.lower() in ['y', 'yes']:
            print("🔄 正在初始化数据库...")
            try:
                result = subprocess.run([sys.executable, "init_canva_db.py"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ 数据库初始化成功")
                    return True
                else:
                    print(f"❌ 数据库初始化失败: {result.stderr}")
                    return False
            except Exception as e:
                print(f"❌ 数据库初始化异常: {e}")
                return False
        else:
            print("请手动初始化数据库: python init_canva_db.py")
            return False

def start_server():
    """启动服务器"""
    print("🚀 启动Canva Backend服务...")
    
    try:
        # 使用uvicorn启动服务
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        print("📡 服务启动命令:", " ".join(cmd))
        print("🌐 服务地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("📖 ReDoc文档: http://localhost:8000/redoc")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 启动服务
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🎨 Canva Backend 启动器")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查环境配置
    if not check_environment():
        return False
    
    # 检查数据库
    if not check_database():
        return False
    
    print("\n✅ 所有检查通过，准备启动服务...")
    print("=" * 40)
    
    # 启动服务器
    start_server()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 再见！")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 启动器异常: {e}")
        sys.exit(1)
