"""
简化的Canva Backend启动脚本
"""
import os
import sys
import uvicorn
from pathlib import Path

def main():
    """启动服务"""
    print("🚀 启动Canva Backend服务...")
    print("🌐 服务地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 使用uvicorn启动
        uvicorn.run(
            "app:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
