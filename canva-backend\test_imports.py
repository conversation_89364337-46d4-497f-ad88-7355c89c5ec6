"""
测试导入和环境设置
"""
import sys
import os
from pathlib import Path

def test_basic_imports():
    """测试基础导入"""
    print("🔍 测试基础导入...")
    
    try:
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
    except ImportError as e:
        print(f"❌ FastAPI导入失败: {e}")
        return False
    
    try:
        import tortoise
        print(f"✅ Tortoise ORM: {tortoise.__version__}")
    except ImportError as e:
        print(f"❌ Tortoise ORM导入失败: {e}")
        return False
    
    try:
        import pydantic
        print(f"✅ Pydantic: {pydantic.__version__}")
    except ImportError as e:
        print(f"❌ Pydantic导入失败: {e}")
        return False
    
    return True

def test_project_imports():
    """测试项目导入"""
    print("\n🔍 测试项目导入...")
    
    # 添加项目路径
    project_root = str(Path(__file__).parent)
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    try:
        from app.settings.config import settings
        print(f"✅ 配置导入成功: {settings.APP_TITLE}")
    except ImportError as e:
        print(f"❌ 配置导入失败: {e}")
        return False
    except AttributeError as e:
        print(f"❌ 配置属性错误: {e}")
        return False
    
    try:
        from app.models.admin import User
        print("✅ 用户模型导入成功")
    except ImportError as e:
        print(f"❌ 用户模型导入失败: {e}")
        return False
    
    try:
        from app.models.canva import Project
        print("✅ Canva模型导入成功")
    except ImportError as e:
        print(f"❌ Canva模型导入失败: {e}")
        return False
    
    return True

def test_environment():
    """测试环境配置"""
    print("\n🔍 测试环境配置...")
    
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env 文件存在")
        return True
    else:
        env_template = Path(".env.canva")
        if env_template.exists():
            print("⚠️ .env 文件不存在，但找到 .env.canva 模板")
            response = input("是否复制 .env.canva 到 .env? (y/n): ")
            if response.lower() in ['y', 'yes']:
                import shutil
                shutil.copy(env_template, env_file)
                print("✅ 已复制 .env.canva 到 .env")
                return True
        
        print("❌ 环境配置文件不存在")
        return False

def main():
    """主函数"""
    print("🧪 Canva Backend 环境测试")
    print("=" * 40)
    
    success = True
    
    # 测试基础导入
    if not test_basic_imports():
        success = False
    
    # 测试环境配置
    if not test_environment():
        success = False
    
    # 测试项目导入
    if not test_project_imports():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 所有测试通过！环境配置正确。")
        print("\n📝 下一步:")
        print("1. 运行: python init_canva_db.py")
        print("2. 启动: python main.py")
    else:
        print("❌ 部分测试失败，请检查环境配置。")
        print("\n🔧 建议:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 复制配置: cp .env.canva .env")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
