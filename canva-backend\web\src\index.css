/* 全局样式重置和Tailwind CSS */
@import 'tailwindcss';

/* 自定义全局样式 */
* {
  box-sizing: border-box;
}

html,
body,
#root {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
}

body {
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ant Design 全局样式覆盖 */
.ant-layout {
  background: #ffffff !important;
}

.ant-layout-sider {
  background: #ffffff !important;
}

/* 自定义Tabs样式 */
.custom-tabs {
  margin-bottom: 0 !important;
}

.custom-tabs .ant-tabs-nav {
  margin: 0 !important;
  background: transparent !important;
  border: none !important;
}

.custom-tabs .ant-tabs-nav::before {
  display: none !important;
}

.custom-tabs .ant-tabs-tab {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-bottom: none !important;
  border-radius: 6px 6px 0 0 !important;
  margin-right: 4px !important;
  padding: 6px 12px !important;
  transition: all 0.2s ease !important;
}

.custom-tabs .ant-tabs-tab:hover {
  background: #f3f4f6 !important;
  border-color: #d1d5db !important;
}

.custom-tabs .ant-tabs-tab-active {
  background: #ffffff !important;
  border-color: #e5e7eb !important;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05) !important;
}

.custom-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #3b82f6 !important;
  font-weight: 500 !important;
}

.custom-tabs .ant-tabs-tab-remove {
  margin-left: 6px !important;
  color: #9ca3af !important;
  transition: color 0.2s ease !important;
}

.custom-tabs .ant-tabs-tab-remove:hover {
  color: #ef4444 !important;
  background: transparent !important;
}

.custom-tabs .ant-tabs-ink-bar {
  display: none !important;
}

/* 响应式字体 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}
