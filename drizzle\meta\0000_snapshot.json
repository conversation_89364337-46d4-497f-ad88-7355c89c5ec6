{"version": "6", "dialect": "sqlite", "id": "90334f4e-0a81-46f2-b113-c288035aaa62", "prevId": "********-0000-0000-0000-************", "tables": {"account": {"name": "account", "columns": {"userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"account_provider_providerAccountId_pk": {"columns": ["provider", "providerAccountId"], "name": "account_provider_providerAccountId_pk"}}, "uniqueConstraints": {}}, "authenticator": {"name": "authenticator", "columns": {"credentialID": {"name": "credentialID", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "credentialPublicKey": {"name": "credentialPublicKey", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "counter": {"name": "counter", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "credentialDeviceType": {"name": "credentialDeviceType", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "credentialBackedUp": {"name": "credentialBackedUp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "transports": {"name": "transports", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"authenticator_credentialID_unique": {"name": "authenticator_credentialID_unique", "columns": ["credentialID"], "isUnique": true}}, "foreignKeys": {"authenticator_userId_user_id_fk": {"name": "authenticator_userId_user_id_fk", "tableFrom": "authenticator", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"authenticator_userId_credentialID_pk": {"columns": ["credentialID", "userId"], "name": "authenticator_userId_credentialID_pk"}}, "uniqueConstraints": {}}, "project": {"name": "project", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "json": {"name": "json", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "height": {"name": "height", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "width": {"name": "width", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "thumbnailUrl": {"name": "thumbnailUrl", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "isTemplate": {"name": "isTemplate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "isPro": {"name": "isPro", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"project_userId_user_id_fk": {"name": "project_userId_user_id_fk", "tableFrom": "project", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "session": {"name": "session", "columns": {"sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "subscription": {"name": "subscription", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscriptionId": {"name": "subscriptionId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customerId": {"name": "customerId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "priceId": {"name": "priceId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "currentPeriodEnd": {"name": "currentPeriodEnd", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"subscription_userId_user_id_fk": {"name": "subscription_userId_user_id_fk", "tableFrom": "subscription", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "emailVerified": {"name": "emailVerified", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "verificationToken": {"name": "verificationToken", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires": {"name": "expires", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationToken_identifier_token_pk": {"columns": ["identifier", "token"], "name": "verificationToken_identifier_token_pk"}}, "uniqueConstraints": {}}}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}