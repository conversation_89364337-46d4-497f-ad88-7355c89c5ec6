{"version": "5.3.0", "objects": [{"type": "rect", "version": "5.3.0", "originX": "center", "originY": "center", "left": 540, "top": 960, "width": 1080, "height": 1920, "fill": "linear-gradient(45deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "name": "background-blur"}, {"type": "rect", "version": "5.3.0", "originX": "center", "originY": "center", "left": 540, "top": 960, "width": 320, "height": 640, "fill": "linear-gradient(180deg, #0f4c75 0%, #3282b8 50%, #0f4c75 100%)", "stroke": "#ffffff", "strokeWidth": 8, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.4)", "blur": 20, "offsetX": 0, "offsetY": 10, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 25, "ry": 25, "name": "phone-frame"}, {"type": "rect", "version": "5.3.0", "originX": "center", "originY": "center", "left": 540, "top": 960, "width": 300, "height": 620, "fill": "#000000", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 20, "ry": 20, "name": "phone-screen"}, {"type": "textbox", "version": "5.3.0", "originX": "center", "originY": "center", "left": 540, "top": 720, "width": 200, "height": 36, "fill": "#ffffff", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "fontSize": 28, "text": "Monday, May 20", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": {}, "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "name": "date-text"}, {"type": "textbox", "version": "5.3.0", "originX": "center", "originY": "center", "left": 540, "top": 800, "width": 300, "height": 120, "fill": "#ffffff", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.3)", "blur": 10, "offsetX": 0, "offsetY": 2, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": "bold", "fontSize": 72, "text": "13:14", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": {}, "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "name": "time-text"}, {"type": "textbox", "version": "5.3.0", "originX": "center", "originY": "center", "left": 540, "top": 1400, "width": 400, "height": 60, "fill": "#ffffff", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON>", "fontWeight": "normal", "fontSize": 36, "text": "一苑 | Wallpaper", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": {}, "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "name": "brand-text"}]}