{"version": "5.3.0", "objects": [{"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 175.5, "top": -286.5, "width": 900, "height": 1200, "fill": "white", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.8)", "blur": 5, "offsetX": 0, "offsetY": 0, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "name": "clip", "selectable": false, "hasControls": false}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": 542.44098913, "top": -751.94941926, "width": 300, "height": 300, "fill": "rgba(239, 185, 185, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 3.31712127, "scaleY": 3.31712127, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": 86.7253306, "top": 1.17957223, "width": 300, "height": 300, "fill": "rgba(168, 129, 106, 1)", "stroke": "rgba(0,0,0,1)", "strokeWidth": 0, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 3.28546147, "scaleY": 3.0284943, "angle": 32.17844516, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "circle", "version": "5.3.0", "originX": "left", "originY": "top", "left": -98.15571418, "top": 171.61322274, "width": 300, "height": 300, "fill": "rgba(0, 0, 0, 0)", "stroke": "rgba(122, 94, 78, 1)", "strokeWidth": 5, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 4.87708757, "scaleY": 3.0284943, "angle": 32.17844516, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "radius": 150, "startAngle": 0, "endAngle": 360, "selectable": true, "hasControls": true}, {"type": "textbox", "version": "5.3.0", "originX": "left", "originY": "top", "left": 355.30538269, "top": 130.68053177, "width": 315.5859375, "height": 195.264, "fill": "rgba(0, 0, 0, 1)", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1.78137457, "scaleY": 1.78137457, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": null, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "fontFamily": "<PERSON><PERSON><PERSON>", "fontWeight": 500, "fontSize": 80, "text": "Coming Soon", "underline": false, "overline": false, "linethrough": false, "textAlign": "center", "fontStyle": "normal", "lineHeight": 1.16, "textBackgroundColor": "", "charSpacing": 0, "styles": [], "direction": "ltr", "path": null, "pathStartOffset": 0, "pathSide": "left", "pathAlign": "baseline", "minWidth": 20, "splitByGrapheme": false, "selectable": true, "hasControls": true, "editable": true}], "clipPath": {"type": "rect", "version": "5.3.0", "originX": "left", "originY": "top", "left": 175.5, "top": -286.5, "width": 900, "height": 1200, "fill": "white", "stroke": null, "strokeWidth": 1, "strokeDashArray": null, "strokeLineCap": "butt", "strokeDashOffset": 0, "strokeLineJoin": "miter", "strokeUniform": false, "strokeMiterLimit": 4, "scaleX": 1, "scaleY": 1, "angle": 0, "flipX": false, "flipY": false, "opacity": 1, "shadow": {"color": "rgba(0,0,0,0.8)", "blur": 5, "offsetX": 0, "offsetY": 0, "affectStroke": false, "nonScaling": false}, "visible": true, "backgroundColor": "", "fillRule": "nonzero", "paintFirst": "fill", "globalCompositeOperation": "source-over", "skewX": 0, "skewY": 0, "rx": 0, "ry": 0, "selectable": true, "hasControls": true}}