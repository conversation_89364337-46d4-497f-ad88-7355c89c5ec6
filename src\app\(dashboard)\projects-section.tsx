"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, MoreHorizon<PERSON>, Trash2, <PERSON><PERSON>, Edit3, Check } from "lucide-react";
import { useRouter } from "next/navigation";
import { formatDistanceToNow } from "date-fns";
import { zhCN } from "date-fns/locale";
import { useState, useCallback } from "react";

import { useGetProjects } from "@/features/projects/api/use-get-projects";
import { useDeleteProject } from "@/features/projects/api/use-delete-project";
import { useDeleteProjects } from "@/features/projects/api/use-delete-projects";
import { useDuplicateProject } from "@/features/projects/api/use-duplicate-project";
import { useRenameProject } from "@/features/projects/api/use-rename-project";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
} from "@/components/ui/context-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export const ProjectsSection = () => {
  const router = useRouter();
  const { data: projects, isLoading, error } = useGetProjects();

  // 项目操作hooks
  const { mutate: deleteProject } = useDeleteProject();
  const { mutate: deleteProjects } = useDeleteProjects();
  const { mutate: duplicateProject } = useDuplicateProject();
  const { mutate: renameProject } = useRenameProject();

  // 状态管理
  const [selectedProjects, setSelectedProjects] = useState<number[]>([]);
  const [editingProject, setEditingProject] = useState<number | null>(null);
  const [editingTitle, setEditingTitle] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<number | null>(null);

  // 多选相关函数
  const toggleProjectSelection = useCallback((projectId: number) => {
    setSelectedProjects(prev =>
      prev.includes(projectId)
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    );
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedProjects([]);
  }, []);

  const selectAll = useCallback(() => {
    if (projects) {
      setSelectedProjects(projects.map(p => p.id));
    }
  }, [projects]);

  // 项目操作函数
  const handleDeleteProject = useCallback((projectId: number) => {
    setProjectToDelete(projectId);
    setDeleteDialogOpen(true);
  }, []);

  const handleDeleteSelected = useCallback(() => {
    if (selectedProjects.length > 0) {
      deleteProjects(selectedProjects);
      clearSelection();
    }
  }, [selectedProjects, deleteProjects, clearSelection]);

  const handleDuplicateProject = useCallback((projectId: number) => {
    duplicateProject(projectId);
  }, [duplicateProject]);

  const handleStartRename = useCallback((project: any) => {
    setEditingProject(project.id);
    setEditingTitle(project.title);
  }, []);

  const handleSaveRename = useCallback(() => {
    if (editingProject && editingTitle.trim()) {
      renameProject({ id: editingProject, title: editingTitle.trim() });
      setEditingProject(null);
      setEditingTitle("");
    }
  }, [editingProject, editingTitle, renameProject]);

  const handleCancelRename = useCallback(() => {
    setEditingProject(null);
    setEditingTitle("");
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">
          Recent projects
        </h3>
        <div className="flex flex-col gap-y-4 items-center justify-center h-32">
          <Loader className="size-6 animate-spin text-muted-foreground" />
          <p className="text-muted-foreground text-sm">
            Loading your projects...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">
          Recent projects
        </h3>
        <div className="flex flex-col gap-y-4 items-center justify-center h-32">
          <FolderOpen className="size-8 text-muted-foreground" />
          <div className="text-center">
            <p className="text-muted-foreground text-sm">
              Failed to load projects
            </p>
            <p className="text-muted-foreground text-xs mt-1">
              Please try refreshing the page
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!projects || projects.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">
          Recent projects
        </h3>
        <div className="flex flex-col gap-y-4 items-center justify-center h-32">
          <FolderOpen className="size-8 text-muted-foreground" />
          <div className="text-center">
            <p className="text-muted-foreground text-sm">
              Your projects will appear here
            </p>
            <p className="text-muted-foreground text-xs mt-1">
              Create a new project to get started
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 调试项目数据
  console.log('📋 Projects data for display:', projects.map(p => ({
    id: p.id,
    title: p.title,
    thumbnail_url: p.thumbnail_url ? 'Has thumbnail' : 'No thumbnail',
    thumbnail_length: p.thumbnail_url?.length || 0
  })));

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-lg">
          Recent projects
        </h3>

        {/* 多选操作栏 */}
        {selectedProjects.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              已选择 {selectedProjects.length} 个项目
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDeleteSelected}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              删除选中
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearSelection}
            >
              取消选择
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {projects.slice(0, 8).map((project) => (
          <ContextMenu key={project.id}>
            <ContextMenuTrigger>
              <div
                className={`group relative bg-white rounded-lg border transition-colors cursor-pointer ${
                  selectedProjects.includes(project.id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={(e) => {
                  if (e.ctrlKey || e.metaKey) {
                    // Ctrl/Cmd + 点击进行多选
                    toggleProjectSelection(project.id);
                  } else if (selectedProjects.length > 0) {
                    // 如果有选中项目，点击切换选择状态
                    toggleProjectSelection(project.id);
                  } else {
                    // 正常点击打开项目
                    router.push(`/editor/${project.id}`);
                  }
                }}
              >
                {/* 选择指示器 */}
                {selectedProjects.includes(project.id) && (
                  <div className="absolute top-2 left-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                    <Check className="w-3 h-3 text-white" />
                  </div>
                )}

                {/* 项目缩略图 */}
                <div className="aspect-[4/3] bg-gray-50 rounded-t-lg flex items-center justify-center">
                  {project.thumbnail_url ? (
                    <img
                      src={project.thumbnail_url}
                      alt={project.title}
                      className="w-full h-full object-cover rounded-t-lg"
                      onLoad={() => console.log('🖼️ Thumbnail loaded for project:', project.id)}
                      onError={(e) => {
                        console.error('❌ Thumbnail failed to load for project:', project.id, e);
                        console.log('❌ Thumbnail URL:', project.thumbnail_url);
                      }}
                    />
                  ) : (
                    <>
                      <FileIcon className="size-8 text-gray-400" />
                      {console.log('📁 No thumbnail for project:', project.id, 'URL:', project.thumbnail_url)}
                    </>
                  )}
                </div>

                {/* 项目信息 */}
                <div className="p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {editingProject === project.id ? (
                        <div className="flex items-center gap-1">
                          <Input
                            value={editingTitle}
                            onChange={(e) => setEditingTitle(e.target.value)}
                            className="h-6 text-sm"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleSaveRename();
                              } else if (e.key === 'Escape') {
                                handleCancelRename();
                              }
                            }}
                            onBlur={handleSaveRename}
                            autoFocus
                          />
                        </div>
                      ) : (
                        <h4 className="font-medium text-sm truncate">
                          {project.title}
                        </h4>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        {formatDistanceToNow(new Date(project.updated_at), {
                          addSuffix: true,
                          locale: zhCN,
                        })}
                      </p>
                    </div>

                    {/* 更多操作菜单 */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/editor/${project.id}`);
                          }}
                        >
                          <Edit3 className="h-4 w-4 mr-2" />
                          打开编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStartRename(project);
                          }}
                        >
                          <Edit3 className="h-4 w-4 mr-2" />
                          重命名
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDuplicateProject(project.id);
                          }}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          复制
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteProject(project.id);
                          }}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </ContextMenuTrigger>

            {/* 右键菜单 */}
            <ContextMenuContent>
              <ContextMenuItem
                onClick={() => router.push(`/editor/${project.id}`)}
              >
                <Edit3 className="h-4 w-4 mr-2" />
                打开编辑
              </ContextMenuItem>
              <ContextMenuItem
                onClick={() => handleStartRename(project)}
              >
                <Edit3 className="h-4 w-4 mr-2" />
                重命名
              </ContextMenuItem>
              <ContextMenuItem
                onClick={() => handleDuplicateProject(project.id)}
              >
                <Copy className="h-4 w-4 mr-2" />
                复制
              </ContextMenuItem>
              <ContextMenuSeparator />
              <ContextMenuItem
                onClick={() => toggleProjectSelection(project.id)}
              >
                <Check className="h-4 w-4 mr-2" />
                {selectedProjects.includes(project.id) ? '取消选择' : '选择'}
              </ContextMenuItem>
              <ContextMenuSeparator />
              <ContextMenuItem
                onClick={() => handleDeleteProject(project.id)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                删除
              </ContextMenuItem>
            </ContextMenuContent>
          </ContextMenu>
        ))}
      </div>

      {/* 查看更多按钮 */}
      {projects.length > 8 && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => router.push('/projects')}
          >
            查看所有项目 ({projects.length})
          </Button>
        </div>
      )}

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除项目</AlertDialogTitle>
            <AlertDialogDescription>
              此操作无法撤销。项目将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setProjectToDelete(null)}>
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (projectToDelete) {
                  deleteProject(projectToDelete);
                  setProjectToDelete(null);
                  setDeleteDialogOpen(false);
                }
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
