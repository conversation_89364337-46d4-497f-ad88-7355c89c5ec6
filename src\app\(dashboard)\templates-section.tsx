"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";

import { localTemplates, loadTemplateData, LocalTemplate } from "@/lib/local-templates";
import { useCreateProject } from "@/features/projects/api/use-create-project";

import { TemplateCard } from "./template-card";

export const TemplatesSection = () => {
  const router = useRouter();
  const mutation = useCreateProject();

  const onClick = async (template: LocalTemplate) => {
    try {
      // 加载模板的实际JSON数据
      const templateJson = await loadTemplateData(template.id);

      // 通过API创建项目
      mutation.mutate(
        {
          title: `${template.name} project`,
          canvas_data: templateJson,
          width: template.width,
          height: template.height,
        },
        {
          onSuccess: (data) => {
            router.push(`/editor/${data.id}`);
          },
        }
      );
    } catch (error) {
      console.error("Failed to create project:", error);
    }
  };

  return (
    <div>
      <h3 className="font-semibold text-lg">
        Start from a template
      </h3>
      <div className="grid grid-cols-2 md:grid-cols-4 mt-4 gap-4">
        {localTemplates.map((template) => (
          <TemplateCard
            key={template.id}
            title={template.name}
            imageSrc={template.thumbnailUrl || ""}
            onClick={() => onClick(template)}
            disabled={mutation.isPending}
            description={`${template.width} x ${template.height} px`}
            width={template.width}
            height={template.height}
            isPro={template.isPro}
          />
        ))}
      </div>
    </div>
  );
};
