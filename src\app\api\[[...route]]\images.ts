import { Hono } from "hono";
import { verifyAuth } from "@hono/auth-js";
import { getLocalImagesWithPlaceholders, defaultLocalImages, type LocalImage } from "@/data/local-images";

// 本地图片存储（在实际应用中，这应该是数据库）
let localImageStore: LocalImage[] = [...defaultLocalImages];

const app = new Hono()
  .get("/", verifyAuth(), async (c) => {
    console.log("🖼️ Local Images API: Fetching local images");

    try {
      // 获取带占位符的本地图片
      const imagesWithPlaceholders = getLocalImagesWithPlaceholders();

      // 转换为与原 API 兼容的格式
      const compatibleImages = imagesWithPlaceholders.map(image => ({
        id: image.id,
        urls: {
          small: image.url,
          regular: image.url,
          full: image.url,
          thumb: image.url,
        },
        links: {
          html: `#image-${image.id}`,
          download: image.url,
        },
        alt_description: image.alt,
        description: image.description,
        user: {
          name: "Local Gallery",
          username: "local",
        },
        width: image.width,
        height: image.height,
        category: image.category,
      }));

      console.log(`🖼️ Returning ${compatibleImages.length} local images`);
      return c.json({ data: compatibleImages });
    } catch (error) {
      console.error("🖼️ Error fetching local images:", error);
      return c.json({ error: "Something went wrong" }, 400);
    }
  })
  .get("/categories", verifyAuth(), async (c) => {
    console.log("🖼️ Local Images API: Fetching categories");

    try {
      const categories = Array.from(new Set(localImageStore.map(img => img.category)));
      return c.json({ data: categories });
    } catch (error) {
      console.error("🖼️ Error fetching categories:", error);
      return c.json({ error: "Something went wrong" }, 400);
    }
  })
  .post("/upload", verifyAuth(), async (c) => {
    console.log("🖼️ Local Images API: Adding uploaded image to gallery");

    try {
      const body = await c.req.json();
      const { url, name, alt, description, category = "uploads" } = body;

      if (!url || !name) {
        return c.json({ error: "Missing required fields" }, 400);
      }

      const newImage: LocalImage = {
        id: `upload-${Date.now()}`,
        name,
        url,
        alt: alt || name,
        description: description || `Uploaded image: ${name}`,
        category,
        width: 800, // 默认尺寸，实际应用中应该从图片文件获取
        height: 600,
        size: 0, // 实际应用中应该从文件获取
        createdAt: new Date().toISOString(),
      };

      localImageStore.push(newImage);

      console.log(`🖼️ Added new image to gallery: ${newImage.name}`);
      return c.json({ data: newImage });
    } catch (error) {
      console.error("🖼️ Error adding image to gallery:", error);
      return c.json({ error: "Something went wrong" }, 400);
    }
  });

export default app;
