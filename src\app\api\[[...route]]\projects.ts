import { z } from "zod";
import { <PERSON>o } from "hono";
import { eq, and, desc, asc } from "drizzle-orm";
import { verifyAuth } from "@hono/auth-js";
import { zValidator } from "@hono/zod-validator";

import { db } from "@/db/drizzle";
import { projects, projectsInsertSchema } from "@/db/schema";
import { mockProjects } from "@/db/mock-db";

// 添加调试标记
console.log("🔄 Projects API loaded");

const app = new Hono()
  .get(
    "/templates",
    verifyAuth(),
    zValidator(
      "query",
      z.object({
        page: z.coerce.number(),
        limit: z.coerce.number(),
      }),
    ),
    async (c) => {
      const { page, limit } = c.req.valid("query");

      const data = await db
        .select()
        .from(projects)
        .where(eq(projects.isTemplate, true))
        .limit(limit)
        .offset((page -1) * limit)
        .orderBy(
          asc(projects.isPro),
          desc(projects.updatedAt),
        );

      return c.json({ data });
    },
  )
  .delete(
    "/:id",
    verifyAuth(),
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      const auth = c.get("authUser");
      const { id } = c.req.valid("param");

      if (!auth.token?.id) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const data = await db
        .delete(projects)
        .where(
          and(
            eq(projects.id, id),
            eq(projects.userId, auth.token.id),
          ),
        )
        .returning();

      if (data.length === 0) {
        return c.json({ error: "Not found" }, 404);
      }

      return c.json({ data: { id } });
    },
  )
  .post(
    "/:id/duplicate",
    verifyAuth(),
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      const auth = c.get("authUser");
      const { id } = c.req.valid("param");

      if (!auth.token?.id) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const data = await db
        .select()
        .from(projects)
        .where(
          and(
            eq(projects.id, id),
            eq(projects.userId, auth.token.id),
          ),
        );

      if (data.length === 0) {
        return c.json({ error:" Not found" }, 404);
      }

      const project = data[0];

      const duplicateData = await db
        .insert(projects)
        .values({
          name: `Copy of ${project.name}`,
          json: project.json,
          width: project.width,
          height: project.height,
          userId: auth.token.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      return c.json({ data: duplicateData[0] });
    },
  )
  .get(
    "/",
    verifyAuth(),
    zValidator(
      "query",
      z.object({
        page: z.coerce.number(),
        limit: z.coerce.number(),
      }),
    ),
    async (c) => {
      const auth = c.get("authUser");
      const { page, limit } = c.req.valid("query");

      if (!auth.token?.id) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      try {
        const dataResult = await db
          .select()
          .from(projects)
          .where(eq(projects.userId, auth.token.id))
          .limit(limit)
          .offset((page - 1) * limit)
          .orderBy(desc(projects.updatedAt))

        // 确保结果是数组
        const data = Array.isArray(dataResult) ? dataResult : [];

        return c.json({
          data,
          nextPage: data.length === limit ? page + 1 : null,
        });
      } catch (error) {
        console.error("Error fetching projects:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    },
  )
  .patch(
    "/:id",
    verifyAuth(),
    zValidator(
      "param",
      z.object({ id: z.string() }),
    ),
    zValidator(
      "json",
      projectsInsertSchema
        .omit({
          id: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
        })
        .partial()
    ),
    async (c) => {
      const auth = c.get("authUser");
      const { id } = c.req.valid("param");
      const values = c.req.valid("json");

      if (!auth.token?.id) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      const data = await db
        .update(projects)
        .set({
          ...values,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(projects.id, id),
            eq(projects.userId, auth.token.id),
          ),
        )
        .returning();

      if (data.length === 0) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      return c.json({ data: data[0] });
    },
  )
  .get(
    "/:id",
    verifyAuth(),
    zValidator("param", z.object({ id: z.string() })),
    async (c) => {
      const auth = c.get("authUser");
      const { id } = c.req.valid("param");

      console.log("🔐 Auth info:", {
        hasAuth: !!auth,
        hasToken: !!auth?.token,
        tokenId: auth?.token?.id,
        authKeys: auth ? Object.keys(auth) : [],
        tokenKeys: auth?.token ? Object.keys(auth.token) : []
      });

      if (!auth.token?.id) {
        console.log("❌ No auth token ID found");
        return c.json({ error: "Unauthorized" }, 401);
      }

      try {
        // 对于模拟数据库，我们需要特殊处理
        const allProjectsResult = await db.select().from(projects);

        // 确保结果是数组
        const allProjects = Array.isArray(allProjectsResult) ? allProjectsResult : [];

        console.log("🔍 Searching for project:", {
          projectId: id,
          userId: auth.token.id,
          totalProjects: allProjects.length,
          projectIds: allProjects.map(p => ({ id: p.id, userId: p.userId })),
          mockProjectsLength: mockProjects.length,
          mockProjectIds: mockProjects.map(p => ({ id: p.id, userId: p.userId }))
        });

        // 手动过滤项目
        const project = allProjects.find((p: any) =>
          p.id === id && p.userId === auth.token?.id
        );

        if (!project) {
          console.log("❌ Project not found:", {
            searchId: id,
            searchUserId: auth.token?.id,
            availableProjects: allProjects.map(p => ({ id: p.id, userId: p.userId, name: p.name }))
          });
          return c.json({ error: "Not found" }, 404);
        }

        console.log("✅ Project found:", { id: project.id, name: project.name });
        return c.json({ data: project });
      } catch (error) {
        console.error("Error fetching project:", error);
        return c.json({ error: "Internal server error" }, 500);
      }
    },
  )
  .post(
    "/",
    verifyAuth(),
    zValidator(
      "json",
      projectsInsertSchema.pick({
        name: true,
        json: true,
        width: true,
        height: true,
      }),
    ),
    async (c) => {
      const auth = c.get("authUser");
      const { name, json, height, width } = c.req.valid("json");

      if (!auth.token?.id) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      console.log("🚀 Creating project:", {
        name,
        userId: auth.token.id,
        width,
        height
      });

      const data = await db
        .insert(projects)
        .values({
          name,
          json,
          width,
          height,
          userId: auth.token.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      if (!data[0]) {
        console.log("❌ Project creation failed");
        return c.json({ error: "Something went wrong" }, 400);
      }

      console.log("✅ Project created:", {
        id: data[0].id,
        name: data[0].name,
        userId: data[0].userId
      });

      return c.json({ data: data[0] });
    },
  );

export default app;
