import { Context, Hono } from "hono";
import { handle } from "hono/vercel";
import { AuthConfig, initAuthConfig } from "@hono/auth-js";

import ai from "./ai";
import users from "./users";
import images from "./images";
import projects from "./projects";
import subscriptions from "./subscriptions";

import authConfig from "@/auth.config";

// Revert to "edge" if planning on running on the edge
export const runtime = "nodejs";

// 检查是否使用FastAPI后端
const USE_FASTAPI_BACKEND = process.env.NEXT_PUBLIC_USE_FASTAPI === 'true';

// 创建禁用响应函数
const createDisabledResponse = () => new Response(
  JSON.stringify({
    error: 'Local API disabled',
    message: 'Using FastAPI backend at ' + process.env.NEXT_PUBLIC_API_URL
  }),
  {
    status: 404,
    headers: { 'Content-Type': 'application/json' }
  }
);

function getAuthConfig(c: Context): AuthConfig {
  // @ts-ignore - NextAuth version compatibility issue
  return {
    secret: process.env.AUTH_SECRET || c.env?.AUTH_SECRET,
    ...authConfig
  };
};

// 根据配置决定使用哪个API
let app: Hono | undefined;
let routes: any;

if (!USE_FASTAPI_BACKEND) {
  app = new Hono().basePath("/api");
  app.use("*", initAuthConfig(getAuthConfig));

  routes = app
    .route("/ai", ai)
    .route("/users", users)
    .route("/images", images)
    .route("/projects", projects)
    .route("/subscriptions", subscriptions);
} else {
  console.log('🔄 FastAPI backend enabled, disabling local API routes');
}

// 导出处理函数
export const GET = USE_FASTAPI_BACKEND ? createDisabledResponse : (app ? handle(app) : createDisabledResponse);
export const POST = USE_FASTAPI_BACKEND ? createDisabledResponse : (app ? handle(app) : createDisabledResponse);
export const PATCH = USE_FASTAPI_BACKEND ? createDisabledResponse : (app ? handle(app) : createDisabledResponse);
export const DELETE = USE_FASTAPI_BACKEND ? createDisabledResponse : (app ? handle(app) : createDisabledResponse);

export type AppType = typeof routes;
