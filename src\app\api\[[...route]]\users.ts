import { z } from "zod";
import { <PERSON>o } from "hono";
import bcrypt from "bcryptjs";
import { zValidator } from "@hono/zod-validator";

// 简单的内存存储，用于演示
const users: Array<{ id: string; name: string; email: string; password: string }> = [
  {
    id: "default-user-id",
    name: "Test User",
    email: "<EMAIL>",
    password: "$2a$12$tFP1lDpA7QlBdW74GUzvFO4GMebdIQTh4DayaXon1G0HojLdnnJby" // 密码是 "password123"
  }
];

const app = new Hono()
  .post(
    "/",
    zValidator(
      "json",
      z.object({
        name: z.string(),
        email: z.string().email(),
        password: z.string().min(3).max(20),
      })
    ),
    async (c) => {
      const { name, email, password } = c.req.valid("json");

      // 检查邮箱是否已存在
      const existingUser = users.find(user => user.email === email);
      if (existingUser) {
        return c.json({ error: "Email already in use" }, 400);
      }

      const hashedPassword = await bcrypt.hash(password, 12);

      // 创建新用户
      const newUser = {
        id: crypto.randomUUID(),
        email,
        name,
        password: hashedPassword,
      };

      users.push(newUser);

      console.log("User created:", { id: newUser.id, email: newUser.email, name: newUser.name });

      return c.json({ success: true, userId: newUser.id }, 200);
    },
  );

export default app;
