import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";

// 模拟图片上传功能
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // 验证文件类型
    if (!file.type.startsWith("image/")) {
      return NextResponse.json({ error: "File must be an image" }, { status: 400 });
    }

    // 验证文件大小 (4MB)
    if (file.size > 4 * 1024 * 1024) {
      return NextResponse.json({ error: "File too large" }, { status: 400 });
    }

    // 在真实应用中，这里会上传到云存储
    // 现在我们返回一个模拟的 URL
    const mockImageUrl = `https://picsum.photos/800/600?random=${Date.now()}`;
    
    console.log(`📸 Mock image upload: ${file.name} (${file.size} bytes)`);
    console.log(`📸 Mock URL: ${mockImageUrl}`);

    return NextResponse.json({
      url: mockImageUrl,
      name: file.name,
      size: file.size,
      type: file.type
    });

  } catch (error) {
    console.error("Image upload error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
