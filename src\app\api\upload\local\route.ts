import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { existsSync } from "fs";

// 本地文件上传 API
export async function POST(req: NextRequest) {
  try {
    console.log("📤 Local Upload: Processing upload request");

    const session = await auth();

    if (!session) {
      console.log("📤 Local Upload: Unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await req.formData();
    const files = formData.getAll("files") as File[];

    if (!files || files.length === 0) {
      console.log("📤 Local Upload: No files provided");
      return NextResponse.json({ error: "No files provided" }, { status: 400 });
    }

    const uploadedFiles = [];

    // 确保上传目录存在
    const uploadDir = join(process.cwd(), "public", "images", "uploads");
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // 验证文件类型
      if (!file.type.startsWith("image/")) {
        throw new Error("File must be an image");
      }

      // 验证文件大小 (4MB)
      if (file.size > 4 * 1024 * 1024) {
        throw new Error("File too large");
      }

      // 生成唯一文件名
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substring(2, 8);
      const fileExtension = file.name.split('.').pop() || 'jpg';
      const fileName = `upload-${timestamp}-${i}-${randomSuffix}.${fileExtension}`;
      
      // 保存文件到本地
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      const filePath = join(uploadDir, fileName);
      
      await writeFile(filePath, buffer);
      
      // 生成可访问的 URL
      const fileUrl = `/images/uploads/${fileName}`;

      console.log(`📤 Local Upload: ${file.name} -> ${fileUrl}`);

      uploadedFiles.push({
        name: file.name,
        size: file.size,
        key: `local-${timestamp}-${i}`,
        url: fileUrl,
        customId: null,
        type: file.type,
        localPath: filePath,
      });
    }

    return NextResponse.json(uploadedFiles);

  } catch (error) {
    console.error("📤 Local Upload error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}

// 获取上传的文件列表
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 这里可以实现获取已上传文件的逻辑
    // 目前返回空数组，实际应用中应该从数据库或文件系统读取
    return NextResponse.json({ files: [] });

  } catch (error) {
    console.error("📤 Local Upload GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
