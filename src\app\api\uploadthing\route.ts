import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";

// 模拟 UploadThing 功能
export async function POST(req: NextRequest) {
  try {
    console.log("📤 Mock UploadThing: Processing upload request");

    const session = await auth();

    if (!session) {
      console.log("📤 Mock UploadThing: Unauthorized");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await req.formData();
    const files = formData.getAll("files") as File[];

    if (!files || files.length === 0) {
      console.log("📤 Mock UploadThing: No files provided");
      return NextResponse.json({ error: "No files provided" }, { status: 400 });
    }

    const uploadedFiles = files.map((file, index) => {
      // 验证文件类型
      if (!file.type.startsWith("image/")) {
        throw new Error("File must be an image");
      }

      // 验证文件大小 (4MB)
      if (file.size > 4 * 1024 * 1024) {
        throw new Error("File too large");
      }

      const mockUrl = `https://picsum.photos/800/600?random=${Date.now()}-${index}`;

      console.log(`📤 Mock UploadThing: ${file.name} -> ${mockUrl}`);

      return {
        name: file.name,
        size: file.size,
        key: `mock-${Date.now()}-${index}`,
        url: mockUrl,
        customId: null,
        type: file.type,
      };
    });

    return NextResponse.json(uploadedFiles);

  } catch (error) {
    console.error("📤 Mock UploadThing error:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ message: "Mock UploadThing endpoint" });
}
