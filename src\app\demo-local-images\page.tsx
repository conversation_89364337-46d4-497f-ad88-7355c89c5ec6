"use client";

import { LocalImageGallery } from "@/components/local-image-gallery";
import { useState } from "react";

export default function DemoLocalImagesPage() {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold text-center mb-8">本地图片画廊演示</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 图片画廊 */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-4 border-b">
              <h2 className="text-xl font-semibold">图片画廊</h2>
              <p className="text-gray-600 text-sm">选择图片查看预览</p>
            </div>
            <div className="h-[600px]">
              <LocalImageGallery
                onImageSelect={(imageUrl) => {
                  setSelectedImage(imageUrl);
                  console.log("Selected image:", imageUrl);
                }}
                showUpload={true}
                viewMode="grid"
              />
            </div>
          </div>

          {/* 预览区域 */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-4 border-b">
              <h2 className="text-xl font-semibold">图片预览</h2>
              <p className="text-gray-600 text-sm">
                {selectedImage ? "当前选中的图片" : "请从左侧选择一张图片"}
              </p>
            </div>
            <div className="h-[600px] flex items-center justify-center p-4">
              {selectedImage ? (
                <div className="max-w-full max-h-full">
                  <img
                    src={selectedImage}
                    alt="Selected image"
                    className="max-w-full max-h-full object-contain rounded-lg shadow-md"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "data:image/svg+xml;base64," + btoa(`
                        <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                          <rect width="100%" height="100%" fill="#f3f4f6"/>
                          <text x="50%" y="50%" font-family="Arial" font-size="16" fill="#9ca3af" text-anchor="middle" dy=".3em">
                            图片加载失败
                          </text>
                        </svg>
                      `);
                    }}
                  />
                  <div className="mt-4 p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-600">图片 URL:</p>
                    <p className="text-xs font-mono bg-white p-2 rounded border break-all">
                      {selectedImage}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gray-200 rounded-lg flex items-center justify-center">
                    <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <p>请选择一张图片进行预览</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 功能说明 */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold mb-4">功能特点</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">✅ 完全本地化</h4>
              <p className="text-sm text-green-700">
                不再依赖外部 API（如 picsum.photos），所有图片都使用本地生成的占位符
              </p>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">📁 本地上传</h4>
              <p className="text-sm text-blue-700">
                支持本地文件上传，文件保存到 public/images/uploads 目录
              </p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <h4 className="font-medium text-purple-800 mb-2">🔍 搜索过滤</h4>
              <p className="text-sm text-purple-700">
                支持按分类过滤和关键词搜索，提供网格和列表两种视图模式
              </p>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg">
              <h4 className="font-medium text-orange-800 mb-2">🎨 SVG 占位符</h4>
              <p className="text-sm text-orange-700">
                使用 SVG 生成的彩色占位符，不同分类有不同的颜色主题
              </p>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <h4 className="font-medium text-red-800 mb-2">⚡ 即时预览</h4>
              <p className="text-sm text-red-700">
                上传文件后立即显示预览，无需等待服务器处理
              </p>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-2">🛡️ 错误处理</h4>
              <p className="text-sm text-gray-700">
                图片加载失败时自动显示占位符，确保界面稳定性
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
