"use client";

import { useEffect, useState } from "react";
import { getLocalImagesWithPlaceholders } from "@/data/local-images";

export default function TestImagesPage() {
  const [images, setImages] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchImages = async () => {
      try {
        // 测试本地图片数据
        const localImages = getLocalImagesWithPlaceholders();
        console.log("Local images:", localImages);
        
        // 测试 API 调用
        const response = await fetch("/api/images");
        if (!response.ok) {
          throw new Error("Failed to fetch images");
        }
        
        const data = await response.json();
        console.log("API response:", data);
        
        setImages(data.data || []);
      } catch (err) {
        console.error("Error fetching images:", err);
        setError(err instanceof Error ? err.message : "Unknown error");
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading images...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center text-red-600">
          <h1 className="text-2xl font-bold mb-4">Error</h1>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">Local Images Test</h1>
        
        <div className="mb-8 p-4 bg-white rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <p className="text-green-600">✅ Successfully loaded {images.length} images</p>
          <p className="text-sm text-gray-600 mt-2">
            All images are now using local placeholders instead of external APIs
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {images.map((image, index) => (
            <div key={image.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="aspect-video relative">
                <img
                  src={image.urls.small}
                  alt={image.alt_description}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "data:image/svg+xml;base64," + btoa(`
                      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                        <rect width="100%" height="100%" fill="#f3f4f6"/>
                        <text x="50%" y="50%" font-family="Arial" font-size="16" fill="#9ca3af" text-anchor="middle" dy=".3em">
                          Error Loading Image
                        </text>
                      </svg>
                    `);
                  }}
                />
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-sm mb-2">{image.alt_description}</h3>
                <p className="text-xs text-gray-600 mb-2">{image.description}</p>
                <div className="flex justify-between items-center text-xs text-gray-500">
                  <span>Category: {image.category}</span>
                  <span>{image.width} × {image.height}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Upload Test</h3>
          <input
            type="file"
            accept="image/*"
            onChange={async (e) => {
              const file = e.target.files?.[0];
              if (!file) return;

              try {
                const formData = new FormData();
                formData.append("files", file);

                const response = await fetch("/api/upload/local", {
                  method: "POST",
                  body: formData,
                });

                if (response.ok) {
                  const result = await response.json();
                  console.log("Upload successful:", result);
                  alert("Upload successful! Check console for details.");
                } else {
                  throw new Error("Upload failed");
                }
              } catch (error) {
                console.error("Upload error:", error);
                alert("Upload failed! Check console for details.");
              }
            }}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <p className="text-sm text-blue-600 mt-2">
            Test local file upload functionality
          </p>
        </div>
      </div>
    </div>
  );
}
