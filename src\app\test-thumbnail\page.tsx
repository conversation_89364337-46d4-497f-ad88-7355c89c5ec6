"use client";

import { useEffect, useRef, useState } from "react";
import { fabric } from "fabric";

export default function TestThumbnailPage() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [thumbnail, setThumbnail] = useState<string | null>(null);

  useEffect(() => {
    if (canvasRef.current) {
      const fabricCanvas = new fabric.Canvas(canvasRef.current, {
        width: 800,
        height: 600,
      });

      // 添加一些测试元素
      const rect = new fabric.Rect({
        left: 100,
        top: 100,
        width: 200,
        height: 150,
        fill: 'red',
      });

      const text = new fabric.Text('Test Thumbnail', {
        left: 150,
        top: 300,
        fontSize: 30,
        fill: 'blue',
      });

      fabricCanvas.add(rect);
      fabricCanvas.add(text);
      fabricCanvas.renderAll();

      setCanvas(fabricCanvas);

      return () => {
        fabricCanvas.dispose();
      };
    }
  }, []);

  const generateThumbnail = () => {
    if (!canvas) return;

    try {
      console.log('📸 Generating test thumbnail...');
      
      const width = canvas.getWidth();
      const height = canvas.getHeight();
      
      console.log('📸 Canvas size:', width, 'x', height);
      
      // 保存当前视口变换
      const currentTransform = canvas.viewportTransform;
      
      // 重置视口变换
      canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
      
      // 生成缩略图
      const multiplier = Math.min(300 / width, 200 / height, 1);
      console.log('📸 Thumbnail multiplier:', multiplier);
      
      const thumbnailDataUrl = canvas.toDataURL({
        format: 'png',
        quality: 0.8,
        multiplier: multiplier
      });
      
      // 恢复视口变换
      if (currentTransform) {
        canvas.setViewportTransform(currentTransform);
      }
      
      console.log('✅ Thumbnail generated successfully, length:', thumbnailDataUrl.length);
      console.log('📸 Thumbnail preview:', thumbnailDataUrl.substring(0, 100) + '...');
      
      setThumbnail(thumbnailDataUrl);
    } catch (error) {
      console.error('❌ Failed to generate thumbnail:', error);
    }
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Thumbnail Generation Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* 原始画布 */}
        <div>
          <h2 className="text-lg font-semibold mb-2">Original Canvas</h2>
          <div className="border border-gray-300">
            <canvas ref={canvasRef} />
          </div>
          <button
            onClick={generateThumbnail}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Generate Thumbnail
          </button>
        </div>

        {/* 生成的缩略图 */}
        <div>
          <h2 className="text-lg font-semibold mb-2">Generated Thumbnail</h2>
          {thumbnail ? (
            <div className="border border-gray-300 p-4">
              <img
                src={thumbnail}
                alt="Generated thumbnail"
                className="max-w-full h-auto border"
                style={{ maxWidth: '300px', maxHeight: '200px' }}
              />
              <div className="mt-2 text-sm text-gray-600">
                <p>Data URL length: {thumbnail.length}</p>
                <p>Format: {thumbnail.substring(5, thumbnail.indexOf(';'))}</p>
              </div>
            </div>
          ) : (
            <div className="border border-gray-300 p-4 text-center text-gray-500">
              Click "Generate Thumbnail" to create a thumbnail
            </div>
          )}
        </div>
      </div>

      {/* 调试信息 */}
      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-2">Debug Information</h2>
        <div className="bg-gray-100 p-4 rounded">
          <p><strong>Canvas initialized:</strong> {canvas ? 'Yes' : 'No'}</p>
          <p><strong>Canvas size:</strong> {canvas ? `${canvas.getWidth()} x ${canvas.getHeight()}` : 'N/A'}</p>
          <p><strong>Objects count:</strong> {canvas ? canvas.getObjects().length : 'N/A'}</p>
          <p><strong>Thumbnail generated:</strong> {thumbnail ? 'Yes' : 'No'}</p>
        </div>
      </div>
    </div>
  );
}
