import { z } from "zod";
import type { NextAuthConfig } from "next-auth";
import { JWT } from "next-auth/jwt";
import Credentials from "next-auth/providers/credentials";
import { authService } from "@/lib/auth-service";
import { extractUserFromJWT } from "@/lib/jwt-utils";

const CredentialsSchema = z.object({
  username: z.string().min(1, "用户名不能为空"),
  password: z.string().min(1, "密码不能为空"),
});

declare module "next-auth/jwt" {
  interface JWT {
    id: string | undefined;
  }
}

declare module "@auth/core/jwt" {
  interface JWT {
    id: string | undefined;
  }
}

export default {
  // 暂时禁用数据库适配器，使用JWT会话
  // adapter: DrizzleAdapter(db),
  providers: [
    Credentials({
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        console.log("🔐 FastAPI Authorization attempt:", { username: credentials?.username });

        const validatedFields = CredentialsSchema.safeParse(credentials);

        if (!validatedFields.success) {
          console.log("❌ Validation failed:", validatedFields.error);
          return null;
        }

        const { username, password } = validatedFields.data;
        console.log("✅ Validation passed for username:", username);

        try {
          // 使用FastAPI后端进行认证
          const tokenResponse = await authService.login({ username, password });
          console.log("🎫 Token received from FastAPI backend");

          // 从JWT token中解析用户信息
          const userInfo = extractUserFromJWT(tokenResponse.access_token);
          if (!userInfo) {
            console.error("❌ Failed to extract user info from JWT");
            return null;
          }

          const returnUser = {
            id: userInfo.id,
            email: userInfo.email,
            name: userInfo.name,
            username: userInfo.username,
            // 将token信息添加到用户对象中，这样NextAuth会话就能访问到
            accessToken: tokenResponse.access_token,
            refreshToken: tokenResponse.refresh_token,
          };
          console.log("✅ FastAPI Login successful, returning user:", returnUser);
          return returnUser;
        } catch (error) {
          console.error("❌ FastAPI Authentication failed:", error);
          return null;
        }
      },
    })
    // 移除社交平台提供者，只保留本地认证
  ],
  pages: {
    signIn: "/sign-in",
    error: "/sign-in"
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    session({ session, token }) {
      if (token.id) {
        session.user.id = token.id;
      }

      // 将token信息添加到会话中
      if (token.accessToken) {
        console.log("🔄 Adding tokens to session");
        (session as any).accessToken = token.accessToken;
        (session as any).refreshToken = token.refreshToken;
        (session as any).tokenExpires = token.tokenExpires;
      }

      return session;
    },
    jwt({ token, user }) {
      if (user) {
        token.id = user.id;

        // 如果是登录时，将token信息保存到JWT中
        if ((user as any).accessToken) {
          console.log("💾 Saving tokens to NextAuth JWT:", {
            hasAccessToken: !!(user as any).accessToken,
            hasRefreshToken: !!(user as any).refreshToken
          });

          token.accessToken = (user as any).accessToken;
          token.refreshToken = (user as any).refreshToken;
          token.tokenExpires = Date.now() + 24 * 60 * 60 * 1000; // 24小时
        }
      }

      return token;
    }
  },
} satisfies NextAuthConfig
