"use client";

import { useEffect, useState } from 'react';
import { checkAuthStatus } from '@/lib/api-client';

export const DebugAuthStatus = () => {
  const [authInfo, setAuthInfo] = useState<any>(null);

  useEffect(() => {
    const checkAuth = () => {
      if (typeof window === 'undefined') return;

      const accessToken = localStorage.getItem('canva_access_token');
      const refreshToken = localStorage.getItem('canva_refresh_token');
      const tokenExpires = localStorage.getItem('canva_token_expires');
      
      const authStatus = checkAuthStatus();
      
      const info = {
        hasAccessToken: !!accessToken,
        hasRefreshToken: !!refreshToken,
        hasExpires: !!tokenExpires,
        accessTokenLength: accessToken?.length || 0,
        accessTokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : 'null',
        refreshTokenLength: refreshToken?.length || 0,
        expires: tokenExpires ? new Date(parseInt(tokenExpires)).toISOString() : 'null',
        isExpired: tokenExpires ? Date.now() >= parseInt(tokenExpires) : true,
        authStatusCheck: authStatus,
        allLocalStorageKeys: Object.keys(localStorage),
        timestamp: new Date().toISOString()
      };
      
      setAuthInfo(info);
      console.log('🔍 Debug Auth Status:', info);
    };

    checkAuth();
    
    // 每5秒检查一次
    const interval = setInterval(checkAuth, 5000);
    
    return () => clearInterval(interval);
  }, []);

  if (!authInfo) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">🔍 Auth Debug Info</h3>
      <div className="space-y-1">
        <div>Access Token: {authInfo.hasAccessToken ? '✅' : '❌'} ({authInfo.accessTokenLength} chars)</div>
        <div>Refresh Token: {authInfo.hasRefreshToken ? '✅' : '❌'} ({authInfo.refreshTokenLength} chars)</div>
        <div>Expires: {authInfo.expires}</div>
        <div>Is Expired: {authInfo.isExpired ? '❌' : '✅'}</div>
        <div>Auth Status: {authInfo.authStatusCheck ? '✅' : '❌'}</div>
        <div>Preview: {authInfo.accessTokenPreview}</div>
        <div className="text-xs opacity-70">Updated: {new Date(authInfo.timestamp).toLocaleTimeString()}</div>
      </div>
    </div>
  );
};
