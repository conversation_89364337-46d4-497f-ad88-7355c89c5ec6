"use client";

import { useState, useEffect } from "react";
import { Search, Upload, Filter, Grid, List } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getLocalImagesWithPlaceholders, imageCategories, type ImageCategory } from "@/data/local-images";

interface LocalImageGalleryProps {
  onImageSelect?: (imageUrl: string) => void;
  showUpload?: boolean;
  viewMode?: "grid" | "list";
}

export function LocalImageGallery({ 
  onImageSelect, 
  showUpload = true, 
  viewMode: initialViewMode = "grid" 
}: LocalImageGalleryProps) {
  const [images, setImages] = useState<any[]>([]);
  const [filteredImages, setFilteredImages] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<ImageCategory>("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">(initialViewMode);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    // 加载本地图片
    const localImages = getLocalImagesWithPlaceholders();
    const compatibleImages = localImages.map(image => ({
      id: image.id,
      urls: {
        small: image.url,
        regular: image.url,
        full: image.url,
        thumb: image.url,
      },
      alt_description: image.alt,
      description: image.description,
      category: image.category,
      width: image.width,
      height: image.height,
    }));
    
    setImages(compatibleImages);
    setFilteredImages(compatibleImages);
  }, []);

  useEffect(() => {
    // 过滤图片
    let filtered = images;

    if (selectedCategory !== "all") {
      filtered = filtered.filter(image => image.category === selectedCategory);
    }

    if (searchTerm) {
      filtered = filtered.filter(image => 
        image.alt_description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        image.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        image.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredImages(filtered);
  }, [images, searchTerm, selectedCategory]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    try {
      // 创建预览 URL
      const previewUrl = URL.createObjectURL(file);
      
      // 添加到本地图片列表
      const newImage = {
        id: `upload-${Date.now()}`,
        urls: {
          small: previewUrl,
          regular: previewUrl,
          full: previewUrl,
          thumb: previewUrl,
        },
        alt_description: file.name,
        description: `Uploaded image: ${file.name}`,
        category: "uploads",
        width: 800,
        height: 600,
      };

      setImages(prev => [newImage, ...prev]);

      // 可选：上传到服务器
      const formData = new FormData();
      formData.append("files", file);

      try {
        const response = await fetch("/api/upload/local", {
          method: "POST",
          body: formData,
        });

        if (response.ok) {
          const result = await response.json();
          console.log("Upload successful:", result);
        }
      } catch (uploadError) {
        console.warn("Server upload failed, but local preview is available:", uploadError);
      }

    } catch (error) {
      console.error("Upload error:", error);
    } finally {
      setIsUploading(false);
      event.target.value = "";
    }
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* 工具栏 */}
      <div className="p-4 border-b bg-white">
        <div className="flex flex-col gap-4">
          {/* 搜索和上传 */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search images..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            {showUpload && (
              <div>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                  id="image-upload"
                  disabled={isUploading}
                />
                <Button
                  asChild
                  variant="outline"
                  disabled={isUploading}
                >
                  <label htmlFor="image-upload" className="cursor-pointer">
                    <Upload className="w-4 h-4 mr-2" />
                    {isUploading ? "Uploading..." : "Upload"}
                  </label>
                </Button>
              </div>
            )}
          </div>

          {/* 过滤器和视图模式 */}
          <div className="flex justify-between items-center">
            <div className="flex gap-2 items-center">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as ImageCategory)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {imageCategories.map(category => (
                  <option key={category} value={category}>
                    {category === "all" ? "All" : category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex gap-1">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 图片网格 */}
      <div className="flex-1 overflow-auto p-4">
        <div className={`${
          viewMode === "grid" 
            ? "grid grid-cols-2 gap-4" 
            : "flex flex-col gap-2"
        }`}>
          {filteredImages.map((image) => (
            <button
              key={image.id}
              onClick={() => onImageSelect?.(image.urls.regular)}
              className={`${
                viewMode === "grid"
                  ? "relative w-full h-[100px] group hover:opacity-75 transition bg-muted rounded-sm overflow-hidden border"
                  : "flex items-center gap-3 p-2 hover:bg-gray-50 rounded border"
              }`}
            >
              <img
                src={image.urls.small}
                alt={image.alt_description}
                className={`${
                  viewMode === "grid"
                    ? "w-full h-full object-cover"
                    : "w-12 h-12 object-cover rounded"
                }`}
                loading="lazy"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "data:image/svg+xml;base64," + btoa(`
                    <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                      <rect width="100%" height="100%" fill="#f3f4f6"/>
                      <text x="50%" y="50%" font-family="Arial" font-size="14" fill="#9ca3af" text-anchor="middle" dy=".3em">
                        Image
                      </text>
                    </svg>
                  `);
                }}
              />
              {viewMode === "list" && (
                <div className="flex-1 text-left">
                  <div className="font-medium text-sm">{image.alt_description}</div>
                  <div className="text-xs text-gray-500">{image.category}</div>
                </div>
              )}
            </button>
          ))}
        </div>

        {filteredImages.length === 0 && (
          <div className="flex flex-col items-center justify-center h-32 text-gray-500">
            <p>No images found</p>
            <p className="text-sm">Try adjusting your search or filters</p>
          </div>
        )}
      </div>

      {/* 状态栏 */}
      <div className="p-2 border-t bg-gray-50 text-xs text-gray-600 text-center">
        {filteredImages.length} of {images.length} images
      </div>
    </div>
  );
}
