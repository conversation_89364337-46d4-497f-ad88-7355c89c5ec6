"use client";

import { useSession } from 'next-auth/react';
import { useEffect } from 'react';

/**
 * 同步NextAuth会话中的token到localStorage
 * 这个组件解决了NextAuth在服务器端运行无法直接访问localStorage的问题
 */
export const TokenSync = () => {
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === 'loading') {
      console.log('🔄 Session loading...');
      return;
    }

    if (status === 'unauthenticated') {
      console.log('🔐 User not authenticated, clearing tokens');
      // 清除localStorage中的token
      if (typeof window !== 'undefined') {
        localStorage.removeItem('canva_access_token');
        localStorage.removeItem('canva_refresh_token');
        localStorage.removeItem('canva_token_expires');
      }
      return;
    }

    if (status === 'authenticated' && session) {
      console.log('✅ User authenticated, syncing tokens');
      
      const sessionWithTokens = session as any;
      
      if (sessionWithTokens.accessToken) {
        console.log('💾 Syncing tokens from NextAuth session to localStorage');
        
        // 将token从NextAuth会话同步到localStorage
        localStorage.setItem('canva_access_token', sessionWithTokens.accessToken);
        
        if (sessionWithTokens.refreshToken) {
          localStorage.setItem('canva_refresh_token', sessionWithTokens.refreshToken);
        }
        
        if (sessionWithTokens.tokenExpires) {
          localStorage.setItem('canva_token_expires', sessionWithTokens.tokenExpires.toString());
        } else {
          // 设置默认过期时间为24小时
          localStorage.setItem('canva_token_expires', (Date.now() + 24 * 60 * 60 * 1000).toString());
        }
        
        console.log('✅ Token sync completed:', {
          hasAccessToken: !!sessionWithTokens.accessToken,
          hasRefreshToken: !!sessionWithTokens.refreshToken,
          accessTokenLength: sessionWithTokens.accessToken?.length || 0,
          accessTokenPreview: sessionWithTokens.accessToken ? 
            `${sessionWithTokens.accessToken.substring(0, 30)}...` : 'null'
        });
        
        // 立即验证存储是否成功
        const storedToken = localStorage.getItem('canva_access_token');
        console.log('🔍 Storage verification:', {
          tokenStored: !!storedToken,
          storedLength: storedToken?.length || 0,
          matches: storedToken === sessionWithTokens.accessToken
        });
      } else {
        console.warn('⚠️ Session authenticated but no access token found');
        console.log('🔍 Session data:', session);
      }
    }
  }, [session, status]);

  // 这个组件不渲染任何UI
  return null;
};
