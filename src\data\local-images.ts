// 本地图片数据
export interface LocalImage {
  id: string;
  name: string;
  url: string;
  alt: string;
  description: string;
  category: string;
  width: number;
  height: number;
  size: number; // 文件大小（字节）
  createdAt: string;
}

// 默认的本地图片集合
export const defaultLocalImages: LocalImage[] = [
  {
    id: "local-1",
    name: "abstract-1.jpg",
    url: "/images/gallery/abstract-1.jpg",
    alt: "Abstract colorful background",
    description: "Vibrant abstract design with flowing colors",
    category: "abstract",
    width: 800,
    height: 600,
    size: 245760,
    createdAt: new Date().toISOString(),
  },
  {
    id: "local-2",
    name: "nature-1.jpg",
    url: "/images/gallery/nature-1.jpg",
    alt: "Beautiful landscape",
    description: "Serene mountain landscape with lake",
    category: "nature",
    width: 800,
    height: 600,
    size: 312450,
    createdAt: new Date().toISOString(),
  },
  {
    id: "local-3",
    name: "business-1.jpg",
    url: "/images/gallery/business-1.jpg",
    alt: "Modern office space",
    description: "Clean modern office environment",
    category: "business",
    width: 800,
    height: 600,
    size: 198320,
    createdAt: new Date().toISOString(),
  },
  {
    id: "local-4",
    name: "technology-1.jpg",
    url: "/images/gallery/technology-1.jpg",
    alt: "Technology concept",
    description: "Digital technology and innovation",
    category: "technology",
    width: 800,
    height: 600,
    size: 267890,
    createdAt: new Date().toISOString(),
  },
  {
    id: "local-5",
    name: "people-1.jpg",
    url: "/images/gallery/people-1.jpg",
    alt: "Team collaboration",
    description: "People working together",
    category: "people",
    width: 800,
    height: 600,
    size: 289340,
    createdAt: new Date().toISOString(),
  },
  {
    id: "local-6",
    name: "food-1.jpg",
    url: "/images/gallery/food-1.jpg",
    alt: "Delicious meal",
    description: "Beautifully presented food",
    category: "food",
    width: 800,
    height: 600,
    size: 234560,
    createdAt: new Date().toISOString(),
  },
];

// 图片分类
export const imageCategories = [
  "all",
  "abstract",
  "nature",
  "business",
  "technology",
  "people",
  "food",
] as const;

export type ImageCategory = typeof imageCategories[number];

// 生成占位符图片 URL（用于开发阶段）
export const generatePlaceholderImage = (width: number = 800, height: number = 600, category: string = "abstract"): string => {
  // 使用不同的颜色主题来区分不同类别
  const colorThemes: Record<string, string> = {
    abstract: "FF6B6B,4ECDC4,45B7D1",
    nature: "2ECC71,27AE60,16A085",
    business: "3498DB,2980B9,1ABC9C",
    technology: "9B59B6,8E44AD,6C3483",
    people: "E67E22,D35400,F39C12",
    food: "E74C3C,C0392B,A93226",
  };
  
  const colors = colorThemes[category] || colorThemes.abstract;
  
  // 创建一个简单的 SVG 占位符
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#${colors.split(',')[0]};stop-opacity:1" />
          <stop offset="50%" style="stop-color:#${colors.split(',')[1]};stop-opacity:1" />
          <stop offset="100%" style="stop-color:#${colors.split(',')[2]};stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" />
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle" dy=".3em">
        ${category.toUpperCase()}
      </text>
      <text x="50%" y="60%" font-family="Arial, sans-serif" font-size="16" fill="white" text-anchor="middle" dy=".3em">
        ${width} × ${height}
      </text>
    </svg>
  `.trim();
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// 为默认图片生成占位符
export const getLocalImagesWithPlaceholders = (): LocalImage[] => {
  return defaultLocalImages.map(image => ({
    ...image,
    url: generatePlaceholderImage(image.width, image.height, image.category),
  }));
};
