import { neon } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import { mockDb } from "./mock-db";

// 尝试连接到数据库，如果失败则使用模拟数据库
let db: any;

try {
  console.log("DATABASE_URL:", process.env.DATABASE_URL);
  if (process.env.DATABASE_URL && process.env.DATABASE_URL !== "postgresql://test:test@localhost:5432/test") {
    console.log("Using real database connection");
    const sql = neon(process.env.DATABASE_URL);
    db = drizzle(sql);
  } else {
    // 使用模拟数据库
    console.log("Using mock database for local development");
    console.log("mockDb methods:", Object.keys(mockDb));
    db = mockDb;
  }
} catch (error) {
  console.log("Database connection failed, using mock database:", error);
  db = mockDb;
}

export { db };
