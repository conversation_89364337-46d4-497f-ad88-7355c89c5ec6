// 简单的内存数据库模拟，用于本地开发
import bcrypt from "bcryptjs";

// 模拟数据
export const mockProjects = [
  {
    id: "template-1",
    name: "Business Card Template",
    json: '{"objects":[]}',
    width: 900,
    height: 1200,
    isTemplate: true,
    isPro: false,
    thumbnailUrl: null,
    userId: "system",
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "template-2",
    name: "Poster Template",
    json: '{"objects":[]}',
    width: 900,
    height: 1200,
    isTemplate: true,
    isPro: true,
    thumbnailUrl: null,
    userId: "system",
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: "project-1",
    name: "My Project",
    json: '{"objects":[]}',
    width: 900,
    height: 1200,
    isTemplate: false,
    isPro: false,
    thumbnailUrl: null,
    userId: "default-user-id",
    createdAt: new Date(),
    updatedAt: new Date(),
  }
];

const mockSubscriptions = [];
const mockUsers = [];

// 全局存储，用于跟踪当前查询的项目ID
let currentProjectId: string | null = null;

// 创建Drizzle风格的模拟数据库
export const mockDb = {
  select: () => ({
    from: (table: any) => {
      console.log("📊 Mock DB: select().from() called with table:", table);

      // 检查table对象的属性
      if (table && typeof table === 'object') {
        console.log("📊 Mock DB: Table object keys:", Object.keys(table));

        // 检查所有Symbol属性
        const symbols = Object.getOwnPropertySymbols(table);
        console.log("📊 Mock DB: Found symbols:", symbols.map(s => s.toString()));

        for (const symbol of symbols) {
          if (symbol.toString().includes('drizzle:Name')) {
            console.log("📊 Mock DB: Found drizzle name symbol:", symbol.toString(), "=", table[symbol]);
          }
        }
      }

      // 获取表名
      let tableName = '';
      if (typeof table === 'string') {
        tableName = table;
      } else if (table && typeof table === 'object') {
        // 尝试从Drizzle表对象获取表名
        const symbols = Object.getOwnPropertySymbols(table);
        for (const symbol of symbols) {
          const symbolStr = symbol.toString();
          console.log("📊 Mock DB: Checking symbol:", symbolStr);
          if (symbolStr.includes('drizzle:Name')) {
            tableName = table[symbol];
            console.log("📊 Mock DB: Found table name from symbol:", tableName);
            break;
          }
        }
      }

      console.log("📊 Mock DB: Resolved table name:", tableName);

      // 返回一个支持链式调用的查询对象
      return {
        // 支持直接执行（当作为Promise使用时）
        then: (resolve: any, reject?: any) => {
          console.log("📊 Mock DB: Direct query execution");
          if (tableName === 'project') {
            console.log("📊 Mock DB: Returning projects:", mockProjects.length, "projects");
            console.log("📊 Mock DB: Project IDs:", mockProjects.map(p => p.id));
            return Promise.resolve(mockProjects).then(resolve, reject);
          }
          if (tableName === 'subscription') {
            console.log("📊 Mock DB: Returning subscriptions:", mockSubscriptions.length, "subscriptions");
            return Promise.resolve(mockSubscriptions).then(resolve, reject);
          }
          return Promise.resolve([]).then(resolve, reject);
        },
        where: (condition: any) => {
          // 对于subscriptions表的where查询，直接返回空数组（表示没有订阅）
          if (table === 'subscriptions') {
            return Promise.resolve([]);
          }

          // 对于projects表的where查询
          if (table === 'projects') {
            // 否则返回所有项目
            return Promise.resolve(mockProjects);
          }

          return {
            limit: (limit: number) => ({
              offset: (offset: number) => ({
                orderBy: (...args: any[]) => {
                  // 根据表类型返回不同的数据
                  if (table === 'projects') {
                    return Promise.resolve(mockProjects.filter(p => p.isTemplate));
                  }
                  return Promise.resolve([]);
                },
              }),
            }),
            orderBy: (...args: any[]) => {
              if (table === 'projects') {
                return Promise.resolve(mockProjects.filter(p => p.isTemplate));
              }
              if (table === 'subscriptions') {
                return Promise.resolve(mockSubscriptions);
              }
              return Promise.resolve([]);
            },
          };
        },
        limit: (limit: number) => ({
          offset: (offset: number) => ({
            orderBy: (...args: any[]) => {
              if (table === 'projects') {
                return Promise.resolve(mockProjects.filter(p => !p.isTemplate));
              }
              return Promise.resolve([]);
            },
          }),
        }),
        orderBy: (...args: any[]) => {
          if (table === 'projects') {
            return Promise.resolve(mockProjects);
          }
          if (table === 'subscriptions') {
            return Promise.resolve(mockSubscriptions);
          }
          return Promise.resolve([]);
        },
      };
    },
  }),

  insert: (table: any) => ({
    values: (data: any) => ({
      returning: () => {
        console.log("📊 Mock DB: insert() called with table:", table);

        // 获取表名
        let tableName = '';
        if (typeof table === 'string') {
          tableName = table;
        } else if (table && typeof table === 'object') {
          // 尝试从Drizzle表对象获取表名
          const symbols = Object.getOwnPropertySymbols(table);
          for (const symbol of symbols) {
            const symbolStr = symbol.toString();
            if (symbolStr.includes('drizzle:Name')) {
              tableName = table[symbol];
              break;
            }
          }
        }

        console.log("📊 Mock DB: Insert resolved table name:", tableName);

        const newItem = { id: crypto.randomUUID(), ...data, createdAt: new Date(), updatedAt: new Date() };
        console.log("📊 Mock DB: Creating new item:", newItem);

        if (tableName === 'project') {
          mockProjects.push(newItem);
          console.log("📊 Mock DB: Added to mockProjects. Total projects:", mockProjects.length);
        }
        return Promise.resolve([newItem]);
      },
    }),
  }),

  update: (table: any) => ({
    set: (data: any) => ({
      where: (condition: any) => ({
        returning: () => Promise.resolve([{ ...data, updatedAt: new Date() }]),
      }),
    }),
  }),

  delete: (table: any) => ({
    where: (condition: any) => Promise.resolve({ rowCount: 1 }),
  }),
};

// 创建一个测试用户
export const createTestUser = async () => {
  const hashedPassword = await bcrypt.hash("password123", 12);
  const testUser = {
    id: "default-user-id",
    name: "Test User",
    email: "<EMAIL>",
    password: hashedPassword,
  };
  mockUsers.push(testUser);
  console.log("Created test user:", testUser);
  return testUser;
};
