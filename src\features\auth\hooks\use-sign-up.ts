import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { authService, RegisterData } from "@/lib/auth-service";

type ResponseType = { message: string; user_id: number };
type RequestType = {
  name: string;
  email: string;
  password: string;
};

export const useSignUp = () => {
  const mutation = useMutation<
    ResponseType,
    Error,
    RequestType
  >({
    mutationFn: async (userData) => {
      // 转换数据格式以匹配FastAPI后端
      const registerData: RegisterData = {
        username: userData.name, // 使用name作为username
        email: userData.email,
        password: userData.password,
        first_name: userData.name,
      };

      return await authService.register(registerData);
    },
    onSuccess: () => {
      toast.success("用户注册成功");
    },
    onError: (error) => {
      console.error("Registration failed:", error);
      toast.error(error.message || "注册失败");
    }
  });

  return mutation;
};
