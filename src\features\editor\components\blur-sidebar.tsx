import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";
import { cn } from "@/lib/utils";

interface BlurSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}

export const BlurSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: BlurSidebarProps) => {
  const [blurValue, setBlurValue] = useState([0]);

  const onClose = () => {
    onChangeActiveTool("select");
  };

  const handleBlurChange = (value: number[]) => {
    setBlurValue(value);
    editor?.changeImageBlur(value[0]);
  };

  const resetBlur = () => {
    setBlurValue([0]);
    editor?.changeImageBlur(0);
  };

  const applyBlur = () => {
    // Blur is applied in real-time, so just close the sidebar
    onClose();
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "blur" ? "visible" : "hidden"
      )}
    >
      <ToolSidebarHeader
        title="Gaussian Blur"
        description="Adjust blur intensity"
      />

      <div className="p-4 space-y-6">
        <div className="space-y-4">
          <div>
            <Label className="text-sm font-medium mb-2 block">
              Blur Intensity: {blurValue[0]}px
            </Label>
            <Slider
              value={blurValue}
              onValueChange={handleBlurChange}
              max={50}
              min={0}
              step={1}
              className="w-full"
            />
          </div>

          <div className="text-xs text-muted-foreground">
            Drag the slider to adjust the blur effect in real-time
          </div>
        </div>

        <div className="space-y-2 pt-4 border-t">
          <Button
            onClick={applyBlur}
            className="w-full"
            size="lg"
          >
            Apply Blur
          </Button>
          
          <Button
            onClick={resetBlur}
            variant="outline"
            className="w-full"
            size="lg"
          >
            Reset
          </Button>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Quick Presets</Label>
          <div className="grid grid-cols-3 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBlurChange([5])}
            >
              Light
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBlurChange([15])}
            >
              Medium
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBlurChange([30])}
            >
              Heavy
            </Button>
          </div>
        </div>
      </div>

      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
