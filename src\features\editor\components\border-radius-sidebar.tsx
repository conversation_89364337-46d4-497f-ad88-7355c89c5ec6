import { useState, useEffect } from "react";
import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";

interface BorderRadiusSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}

export const BorderRadiusSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: BorderRadiusSidebarProps) => {
  const [borderRadius, setBorderRadius] = useState(0);

  const onClose = () => {
    onChangeActiveTool("select");
  };

  // Get current border radius from selected object
  useEffect(() => {
    if (!editor?.canvas) return;

    const updateBorderRadius = () => {
      const activeObject = editor.canvas.getActiveObject();
      if (activeObject && (activeObject.type === 'rect' || activeObject.type === 'group')) {
        const rx = (activeObject as any).rx || 0;
        setBorderRadius(rx);
      }
    };

    updateBorderRadius();

    const handleSelectionChange = () => {
      updateBorderRadius();
    };

    editor.canvas.on('selection:created', handleSelectionChange);
    editor.canvas.on('selection:updated', handleSelectionChange);
    editor.canvas.on('selection:cleared', () => setBorderRadius(0));

    return () => {
      editor.canvas.off('selection:created', handleSelectionChange);
      editor.canvas.off('selection:updated', handleSelectionChange);
      editor.canvas.off('selection:cleared', handleSelectionChange);
    };
  }, [editor]);

  const onChangeBorderRadius = (value: number[]) => {
    const radius = value[0];
    setBorderRadius(radius);
    
    if (!editor?.canvas) return;

    const activeObjects = editor.canvas.getActiveObjects();
    activeObjects.forEach((object) => {
      if (object.type === 'rect') {
        object.set({
          rx: radius,
          ry: radius,
        });
      } else if (object.type === 'group') {
        // Apply to all rectangles in the group
        (object as any).getObjects().forEach((obj: any) => {
          if (obj.type === 'rect') {
            obj.set({
              rx: radius,
              ry: radius,
            });
          }
        });
      }
    });

    editor.canvas.renderAll();
  };

  const presetRadiusValues = [0, 5, 10, 15, 20, 25, 30, 40, 50];

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "border-radius" ? "visible" : "hidden",
      )}
    >
      <ToolSidebarHeader
        title="圆角"
        description="调整形状的圆角半径"
      />

      <div className="p-4 space-y-6">
        <div className="space-y-2">
          <Label>圆角半径: {borderRadius}px</Label>
          <Slider
            value={[borderRadius]}
            onValueChange={onChangeBorderRadius}
            max={100}
            min={0}
            step={1}
            className="w-full"
          />
        </div>

        <div className="space-y-2">
          <Label>预设值</Label>
          <div className="grid grid-cols-3 gap-2">
            {presetRadiusValues.map((value) => (
              <Button
                key={value}
                variant={borderRadius === value ? "default" : "outline"}
                size="sm"
                onClick={() => onChangeBorderRadius([value])}
                className="text-xs"
              >
                {value}px
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Label>圆角预览</Label>
          <div className="grid grid-cols-4 gap-2">
            {[0, 10, 20, 50].map((radius) => (
              <Button
                key={radius}
                variant="outline"
                size="sm"
                onClick={() => onChangeBorderRadius([radius])}
                className="h-12 p-2"
              >
                <div
                  className="w-8 h-8 bg-blue-500"
                  style={{
                    borderRadius: `${radius}px`,
                  }}
                />
              </Button>
            ))}
          </div>
        </div>
      </div>

      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
