import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";
import { cn } from "@/lib/utils";
import { Crop, Square, Circle, Triangle } from "lucide-react";

interface CropSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}

const cropPresets = [
  { name: "Free", ratio: null, icon: Crop },
  { name: "Square", ratio: 1, icon: Square },
  { name: "16:9", ratio: 16/9, icon: Square },
  { name: "4:3", ratio: 4/3, icon: Square },
  { name: "3:2", ratio: 3/2, icon: Square },
];

export const CropSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: CropSidebarProps) => {
  const [cropMode, setCropMode] = useState(false);
  const [selectedRatio, setSelectedRatio] = useState<number | null>(null);

  const onClose = () => {
    onChangeActiveTool("select");
    setCropMode(false);
  };

  const startCrop = (ratio: number | null) => {
    setSelectedRatio(ratio);
    setCropMode(true);
    editor?.startCrop(ratio);
  };

  const applyCrop = () => {
    editor?.applyCrop();
    setCropMode(false);
    onClose();
  };

  const cancelCrop = () => {
    editor?.cancelCrop();
    setCropMode(false);
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "crop" ? "visible" : "hidden"
      )}
    >
      <ToolSidebarHeader
        title="Crop Image"
        description="Select crop area and apply"
      />

      <div className="p-4 space-y-4">
        {!cropMode ? (
          <>
            <div>
              <Label className="text-sm font-medium mb-2 block">
                Crop Presets
              </Label>
              <div className="grid grid-cols-2 gap-2">
                {cropPresets.map((preset) => {
                  const Icon = preset.icon;
                  return (
                    <Button
                      key={preset.name}
                      variant="outline"
                      className="h-16 flex flex-col gap-1"
                      onClick={() => startCrop(preset.ratio)}
                    >
                      <Icon className="size-4" />
                      <span className="text-xs">{preset.name}</span>
                    </Button>
                  );
                })}
              </div>
            </div>

            <div className="pt-4 border-t">
              <Button
                onClick={() => startCrop(null)}
                className="w-full"
                size="lg"
              >
                Start Free Crop
              </Button>
            </div>
          </>
        ) : (
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Drag the corners to adjust the crop area
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={applyCrop}
                className="flex-1"
                size="lg"
              >
                Apply Crop
              </Button>
              <Button
                onClick={cancelCrop}
                variant="outline"
                className="flex-1"
                size="lg"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </div>

      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
