"use client";

import { fabric } from "fabric";
import debounce from "lodash.debounce";
import { useCallback, useEffect, useRef, useState } from "react";

import { ResponseType } from "@/features/projects/api/use-get-project";
import { useAutoSaveProject } from "@/features/projects/api/use-auto-save-project";
import { useUpdateProject } from "@/features/projects/api/use-update-project";
import { Project } from "@/lib/project-service";

import { 
  ActiveTool, 
  selectionDependentTools
} from "@/features/editor/types";
import { Navbar } from "@/features/editor/components/navbar";
import { Footer } from "@/features/editor/components/footer";
import { useEditor } from "@/features/editor/hooks/use-editor";
import { Sidebar } from "@/features/editor/components/sidebar";
import { Toolbar } from "@/features/editor/components/toolbar";
import { ShapeSidebar } from "@/features/editor/components/shape-sidebar";
import { FillColorSidebar } from "@/features/editor/components/fill-color-sidebar";
import { StrokeColorSidebar } from "@/features/editor/components/stroke-color-sidebar";
import { StrokeWidthSidebar } from "@/features/editor/components/stroke-width-sidebar";
import { OpacitySidebar } from "@/features/editor/components/opacity-sidebar";
import { TextSidebar } from "@/features/editor/components/text-sidebar";
import { FontSidebar } from "@/features/editor/components/font-sidebar";
import { ImageSidebar } from "@/features/editor/components/image-sidebar";
import { FilterSidebar } from "@/features/editor/components/filter-sidebar";
import { DrawSidebar } from "@/features/editor/components/draw-sidebar";
import { AiSidebar } from "@/features/editor/components/ai-sidebar";
import { TemplateSidebar } from "@/features/editor/components/template-sidebar";
import { RemoveBgSidebar } from "@/features/editor/components/remove-bg-sidebar";
import { SettingsSidebar } from "@/features/editor/components/settings-sidebar";
import { CropSidebar } from "@/features/editor/components/crop-sidebar";
import { BlurSidebar } from "@/features/editor/components/blur-sidebar";
import { MaskSidebar } from "@/features/editor/components/mask-sidebar";
import { LayersSidebar } from "@/features/editor/components/layers-sidebar";
import { BorderRadiusSidebar } from "@/features/editor/components/border-radius-sidebar";

interface EditorProps {
  initialData: Project;
};

export const Editor = ({ initialData }: EditorProps) => {
  const { mutate: autoSave, isPending: isAutoSaving, error: autoSaveError } = useAutoSaveProject(initialData.id);
  const { mutate: manualSave, isPending: isManualSaving, error: manualSaveError } = useUpdateProject(initialData.id);

  // 保存状态管理
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // 监听保存完成事件
  useEffect(() => {
    if (!isAutoSaving && !autoSaveError) {
      setLastSaved(new Date());
    }
  }, [isAutoSaving, autoSaveError]);

  useEffect(() => {
    if (!isManualSaving && !manualSaveError) {
      setLastSaved(new Date());
    }
  }, [isManualSaving, manualSaveError]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSave = useCallback(
    debounce(
      (values: {
        canvas_data: any,
        height: number,
        width: number,
        thumbnail_url?: string | null,
      }) => {
        console.log("🔄 Auto-saving with data:", values);

        // 确保数据格式正确
        const saveData = {
          canvas_data: values.canvas_data || {},
          height: values.height || initialData.height,
          width: values.width || initialData.width,
          thumbnail_url: values.thumbnail_url || null,
        };

        console.log("🔄 Formatted save data:", saveData);
        autoSave(saveData);
    },
    500
  ), [autoSave, initialData.height, initialData.width]);

  const [activeTool, setActiveTool] = useState<ActiveTool>("select");

  const onClearSelection = useCallback(() => {
    if (selectionDependentTools.includes(activeTool)) {
      setActiveTool("select");
    }
  }, [activeTool]);

  const { init, editor } = useEditor({
    defaultState: initialData.canvas_data,
    defaultWidth: initialData.width,
    defaultHeight: initialData.height,
    clearSelectionCallback: onClearSelection,
    saveCallback: debouncedSave,
  });

  const handleManualSave = useCallback(() => {
    if (editor) {
      const workspace = editor.getWorkspace();
      const canvas_data = editor.canvas.toJSON();
      const height = workspace?.height || initialData.height;
      const width = workspace?.width || initialData.width;

      // 生成缩略图
      const generateThumbnail = () => {
        try {
          console.log('📸 Manual save: Generating thumbnail...');

          const canvas = editor.canvas;
          const canvasWidth = canvas.getWidth();
          const canvasHeight = canvas.getHeight();

          console.log('📸 Manual save: Canvas size:', canvasWidth, 'x', canvasHeight);

          if (canvasWidth <= 0 || canvasHeight <= 0) {
            console.error('❌ Manual save: Invalid canvas size for thumbnail generation');
            return null;
          }

          const currentTransform = canvas.viewportTransform;
          canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

          // 获取工作区信息
          const workspace = editor.getWorkspace();
          const workspaceWidth = workspace?.width || width;
          const workspaceHeight = workspace?.height || height;

          console.log('📸 Manual save: Workspace size:', workspaceWidth, 'x', workspaceHeight);

          // 提高缩略图尺寸和质量，基于工作区尺寸
          const maxThumbnailSize = 400;
          const aspectRatio = workspaceWidth / workspaceHeight;

          let thumbnailWidth, thumbnailHeight;
          if (aspectRatio > 1) {
            thumbnailWidth = Math.min(maxThumbnailSize, workspaceWidth);
            thumbnailHeight = thumbnailWidth / aspectRatio;
          } else {
            thumbnailHeight = Math.min(maxThumbnailSize, workspaceHeight);
            thumbnailWidth = thumbnailHeight * aspectRatio;
          }

          const multiplier = Math.min(
            thumbnailWidth / workspaceWidth,
            thumbnailHeight / workspaceHeight,
            2 // 允许2倍放大以提高清晰度
          );

          console.log('📸 Manual save: Thumbnail multiplier:', multiplier);
          console.log('📸 Manual save: Target size:', `${thumbnailWidth}x${thumbnailHeight}`);

          // 找到工作区对象获取位置
          const workspaceObj = canvas.getObjects().find((obj) => obj.name === "clip");
          const workspaceLeft = workspaceObj?.left || 0;
          const workspaceTop = workspaceObj?.top || 0;

          console.log('📸 Manual save: Workspace position:', `${workspaceLeft}, ${workspaceTop}`);

          // 截取整个canvas以包含滤镜效果
          const thumbnailDataUrl = canvas.toDataURL({
            format: 'png',
            quality: 0.9,
            multiplier: multiplier
            // 不指定区域，截取整个canvas包含所有效果
          });

          if (currentTransform) {
            canvas.setViewportTransform(currentTransform);
          }

          console.log('✅ Manual save: Thumbnail generated successfully, length:', thumbnailDataUrl.length);

          // 检查缩略图大小，如果太大则压缩
          if (thumbnailDataUrl.length > 500000) { // 500KB限制（提高限制）
            console.log('⚠️ Manual save: Thumbnail too large, compressing...');

            // 尝试JPEG格式压缩
            const compressedThumbnail = canvas.toDataURL({
              format: 'jpeg',
              quality: 0.7, // 提高压缩质量
              multiplier: multiplier * 0.8 // 稍微减小尺寸
            });

            console.log('✅ Manual save: Compressed thumbnail length:', compressedThumbnail.length);
            return compressedThumbnail;
          }

          return thumbnailDataUrl;
        } catch (error) {
          console.error('❌ Manual save: Failed to generate thumbnail:', error);
          return null;
        }
      };

      const thumbnail = generateThumbnail();

      console.log('💾 Manual save: Saving with thumbnail:', thumbnail ? 'Generated' : 'Failed');

      manualSave({
        canvas_data,
        height,
        width,
        thumbnail_url: thumbnail
      });
    }
  }, [editor, manualSave, initialData.height, initialData.width]);

  const handleTestThumbnail = useCallback(() => {
    if (editor) {
      console.log('🧪 Testing thumbnail generation...');

      const canvas = editor.canvas;
      const canvasWidth = canvas.getWidth();
      const canvasHeight = canvas.getHeight();

      console.log('🧪 Test: Canvas size:', canvasWidth, 'x', canvasHeight);
      console.log('🧪 Test: Canvas objects:', canvas.getObjects().length);

      try {
        const currentTransform = canvas.viewportTransform;
        canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

        // 获取工作区信息
        const workspace = editor.getWorkspace();
        const workspaceWidth = workspace?.width || 800;
        const workspaceHeight = workspace?.height || 600;

        console.log('🧪 Test: Workspace size:', workspaceWidth, 'x', workspaceHeight);

        // 使用与手动保存相同的高质量设置，基于工作区尺寸
        const maxThumbnailSize = 400;
        const aspectRatio = workspaceWidth / workspaceHeight;

        let thumbnailWidth, thumbnailHeight;
        if (aspectRatio > 1) {
          thumbnailWidth = Math.min(maxThumbnailSize, workspaceWidth);
          thumbnailHeight = thumbnailWidth / aspectRatio;
        } else {
          thumbnailHeight = Math.min(maxThumbnailSize, workspaceHeight);
          thumbnailWidth = thumbnailHeight * aspectRatio;
        }

        const multiplier = Math.min(
          thumbnailWidth / workspaceWidth,
          thumbnailHeight / workspaceHeight,
          2
        );

        console.log('🧪 Test: Thumbnail multiplier:', multiplier);
        console.log('🧪 Test: Target size:', `${thumbnailWidth}x${thumbnailHeight}`);

        // 找到工作区对象获取位置
        const workspaceObj = canvas.getObjects().find((obj) => obj.name === "clip");
        const workspaceLeft = workspaceObj?.left || 0;
        const workspaceTop = workspaceObj?.top || 0;

        console.log('🧪 Test: Workspace position:', `${workspaceLeft}, ${workspaceTop}`);

        // 截取整个canvas以包含滤镜效果
        const thumbnailDataUrl = canvas.toDataURL({
          format: 'png',
          quality: 0.9,
          multiplier: multiplier
          // 不指定区域，截取整个canvas包含所有效果
        });

        if (currentTransform) {
          canvas.setViewportTransform(currentTransform);
        }

        console.log('✅ Test: Thumbnail generated successfully!');
        console.log('✅ Test: Thumbnail length:', thumbnailDataUrl.length);
        console.log('✅ Test: Thumbnail preview:', thumbnailDataUrl.substring(0, 100) + '...');

        // 显示缩略图在新窗口中
        const newWindow = window.open();
        if (newWindow) {
          newWindow.document.write(`
            <html>
              <head><title>Thumbnail Test</title></head>
              <body style="margin: 20px; font-family: Arial;">
                <h2>Thumbnail Test Result</h2>
                <p><strong>Size:</strong> ${thumbnailDataUrl.length} characters</p>
                <p><strong>Canvas:</strong> ${canvasWidth} x ${canvasHeight}</p>
                <p><strong>Multiplier:</strong> ${multiplier}</p>
                <img src="${thumbnailDataUrl}" style="border: 1px solid #ccc; max-width: 300px;" />
              </body>
            </html>
          `);
        }

      } catch (error) {
        console.error('❌ Test: Failed to generate thumbnail:', error);
      }
    } else {
      console.error('❌ Test: Editor not available');
    }
  }, [editor]);

  const onChangeActiveTool = useCallback((tool: ActiveTool) => {
    if (tool === "draw") {
      editor?.enableDrawingMode();
    }

    if (activeTool === "draw") {
      editor?.disableDrawingMode();
    }

    if (tool === activeTool) {
      return setActiveTool("select");
    }
    
    setActiveTool(tool);
  }, [activeTool, editor]);

  const canvasRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const canvas = new fabric.Canvas(canvasRef.current, {
      controlsAboveOverlay: true,
      preserveObjectStacking: true,
      selection: true, // 启用多选
      selectionKey: 'ctrlKey', // Ctrl键多选
      selectionColor: 'rgba(59, 130, 246, 0.1)', // 选择框颜色
      selectionBorderColor: '#3b82f6', // 选择框边框颜色
      selectionLineWidth: 2, // 选择框边框宽度
    });

    // 添加滚轮缩放和平移功能
    canvas.on('mouse:wheel', function(opt) {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();

      if (opt.e.ctrlKey) {
        // Ctrl + 滚轮：缩放
        zoom *= 0.999 ** delta;
        if (zoom > 20) zoom = 20;
        if (zoom < 0.01) zoom = 0.01;
        canvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
        opt.e.preventDefault();
        opt.e.stopPropagation();
      } else if (opt.e.altKey) {
        // Alt + 滚轮：垂直平移
        const vpt = canvas.viewportTransform;
        if (vpt) {
          vpt[5] -= delta;
          canvas.requestRenderAll();
        }
        opt.e.preventDefault();
        opt.e.stopPropagation();
      } else if (opt.e.shiftKey) {
        // Shift + 滚轮：水平平移
        const vpt = canvas.viewportTransform;
        if (vpt) {
          vpt[4] -= delta;
          canvas.requestRenderAll();
        }
        opt.e.preventDefault();
        opt.e.stopPropagation();
      }
    });

    init({
      initialCanvas: canvas,
      initialContainer: containerRef.current!,
    });

    return () => {
      canvas.dispose();
    };
  }, [init]);

  return (
    <div className="h-full flex flex-col">
      <Navbar
        id={initialData.id}
        editor={editor}
        activeTool={activeTool}
        onChangeActiveTool={onChangeActiveTool}
        isSaving={isAutoSaving || isManualSaving}
        hasError={!!autoSaveError || !!manualSaveError}
      />
      <div className="absolute h-[calc(100%-68px)] w-full top-[68px] flex">
        <Sidebar
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ShapeSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FillColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeColorSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <StrokeWidthSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <OpacitySidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TextSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FontSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <ImageSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <TemplateSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <LayersSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <BorderRadiusSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <CropSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <BlurSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <MaskSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <FilterSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <AiSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <RemoveBgSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <DrawSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <SettingsSidebar
          editor={editor}
          activeTool={activeTool}
          onChangeActiveTool={onChangeActiveTool}
        />
        <main className="bg-muted flex-1 overflow-auto relative flex flex-col">
          <Toolbar
            editor={editor}
            activeTool={activeTool}
            onChangeActiveTool={onChangeActiveTool}
            onSave={handleManualSave}
            onTestThumbnail={handleTestThumbnail}
            isSaving={isAutoSaving || isManualSaving}
            hasError={!!autoSaveError || !!manualSaveError}
            key={JSON.stringify(editor?.canvas.getActiveObject())}
          />
          <div className="flex-1 h-[calc(100%-124px)] bg-muted" ref={containerRef}>
            <canvas ref={canvasRef} />
          </div>
          <Footer editor={editor} />
        </main>
      </div>
    </div>
  );
};
