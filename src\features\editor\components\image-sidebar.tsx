import { useState } from "react";
import { Upload, Loader } from "lucide-react";
import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { getLocalImagesWithPlaceholders } from "@/data/local-images";
import { cn } from "@/lib/utils";

interface ImageSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}



// 简化的本地上传组件
const SimpleUploadButton = ({ onUpload }: { onUpload: (url: string) => void }) => {
  const [isUploading, setIsUploading] = useState(false);

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);

    try {
      // 创建本地预览 URL
      const previewUrl = URL.createObjectURL(file);
      onUpload(previewUrl);
      console.log("📤 Using local preview URL:", previewUrl);
    } catch (error) {
      console.error("📤 Upload error:", error);
    } finally {
      setIsUploading(false);
      event.target.value = "";
    }
  };

  return (
    <div className="w-full">
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        id="image-upload"
        disabled={isUploading}
      />
      <Button
        asChild
        className="w-full text-sm font-medium"
        disabled={isUploading}
      >
        <label htmlFor="image-upload" className="cursor-pointer">
          {isUploading ? (
            <>
              <Loader className="w-4 h-4 mr-2 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="w-4 h-4 mr-2" />
              Upload Image
            </>
          )}
        </label>
      </Button>
    </div>
  );
};

export const ImageSidebar = ({ editor, activeTool, onChangeActiveTool }: ImageSidebarProps) => {
  const [images] = useState(() => {
    // 获取本地图片数据
    const localImages = getLocalImagesWithPlaceholders();
    return localImages.map(image => ({
      id: image.id,
      urls: {
        small: image.url,
        regular: image.url,
        full: image.url,
        thumb: image.url,
      },
      alt_description: image.alt,
      description: image.description,
      category: image.category,
      width: image.width,
      height: image.height,
    }));
  });

  const onClose = () => {
    onChangeActiveTool("select");
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "images" ? "visible" : "hidden"
      )}
    >
      <ToolSidebarHeader title="Images" description="Add images to your canvas" />

      {/* 上传按钮 */}
      <div className="p-4 border-b">
        <SimpleUploadButton
          onUpload={(url) => {
            editor?.addImage(url);
          }}
        />
      </div>

      {/* 图片网格 */}
      <ScrollArea>
        <div className="p-4">
          <div className="grid grid-cols-2 gap-4">
            {images.map((image) => (
              <button
                onClick={() => editor?.addImage(image.urls.regular)}
                key={image.id}
                className="relative w-full h-[100px] group hover:opacity-75 transition bg-muted rounded-sm overflow-hidden border"
              >
                <img
                  src={image.urls.small}
                  alt={image.alt_description}
                  className="object-cover w-full h-full"
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "data:image/svg+xml;base64," + btoa(`
                      <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                        <rect width="100%" height="100%" fill="#f3f4f6"/>
                        <text x="50%" y="50%" font-family="Arial" font-size="14" fill="#9ca3af" text-anchor="middle" dy=".3em">
                          Image
                        </text>
                      </svg>
                    `);
                  }}
                />
                <div className="opacity-0 group-hover:opacity-100 absolute left-0 bottom-0 w-full text-[10px] truncate text-white p-1 bg-black/50 text-left">
                  {image.category}
                </div>
              </button>
            ))}
          </div>
        </div>
      </ScrollArea>

      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
