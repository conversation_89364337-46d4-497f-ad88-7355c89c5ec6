import { useState, useEffect, useCallback, useRef } from "react";
import { fabric } from "fabric";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuShortcut,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";
import { cn } from "@/lib/utils";
import {
  Eye,
  EyeOff,
  Lock,
  Unlock,
  ChevronDown,
  ChevronRight,
  Folder,
  FolderOpen,
  Image,
  Type,
  Square,
  Plus,
  Trash2,
  Copy,
  ArrowUp,
  ArrowDown,
  Edit,
  Group,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  GripVertical
} from "lucide-react";

interface LayersSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}

interface LayerItem {
  id: string;
  name: string;
  type: 'image' | 'text' | 'shape' | 'group';
  visible: boolean;
  locked: boolean;
  children?: LayerItem[];
  expanded?: boolean;
  isShapeMask?: boolean;
  maskReference?: string;
  groupId?: string; // 所属分组ID
  parentId?: string; // 父级ID（用于嵌套分组）
}

export const LayersSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: LayersSidebarProps) => {
  const [layers, setLayers] = useState<LayerItem[]>([]);
  const [selectedLayer, setSelectedLayer] = useState<string | null>(null);
  const [selectedLayers, setSelectedLayers] = useState<string[]>([]); // 多选状态
  const [draggedLayer, setDraggedLayer] = useState<string | null>(null);
  const [dragOverLayer, setDragOverLayer] = useState<string | null>(null);
  const [editingLayer, setEditingLayer] = useState<string | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    layerId: string;
    isGroup: boolean;
  } | null>(null); // 重命名状态

  // 防抖的蒙版更新函数 - 使用 requestAnimationFrame
  const debouncedUpdateMasks = useRef<number>();

  const onClose = () => {
    onChangeActiveTool("select");
  };

  // 处理图层点击（支持多选）
  const handleLayerClick = (layerId: string, event: React.MouseEvent, layer: LayerItem) => {
    event.stopPropagation();

    if (layer.type === 'group') {
      // 点击分组，选择组内所有对象
      const groupChildren = layer.children || [];
      const childIds = groupChildren.map(child => child.id);

      if (childIds.length > 0) {
        const objects = editor?.canvas.getObjects() || [];
        const childObjects = childIds.map(id =>
          objects.find(obj => (obj as any).id === id)
        ).filter(Boolean) as fabric.Object[];

        if (childObjects.length > 0) {
          editor?.canvas.discardActiveObject();
          if (childObjects.length === 1) {
            editor?.canvas.setActiveObject(childObjects[0]);
          } else {
            const selection = new fabric.ActiveSelection(childObjects, {
              canvas: editor.canvas
            });
            editor?.canvas.setActiveObject(selection);
          }
          editor?.canvas.renderAll();
        }
      }

      setSelectedLayers(childIds);
      setSelectedLayer(layerId);
      return;
    }

    // 处理普通图层点击
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => (obj as any).id === layerId);

    if (!targetObject) return;

    if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd + 点击：多选
      const newSelectedLayers = selectedLayers.includes(layerId)
        ? selectedLayers.filter(id => id !== layerId)
        : [...selectedLayers, layerId];

      setSelectedLayers(newSelectedLayers);

      // 更新画布选择
      const selectedObjects = newSelectedLayers.map(id =>
        objects.find(obj => (obj as any).id === id)
      ).filter(Boolean) as fabric.Object[];

      editor?.canvas.discardActiveObject();
      if (selectedObjects.length === 1) {
        editor?.canvas.setActiveObject(selectedObjects[0]);
        setSelectedLayer(newSelectedLayers[0]);
      } else if (selectedObjects.length > 1) {
        const selection = new fabric.ActiveSelection(selectedObjects, {
          canvas: editor.canvas
        });
        editor?.canvas.setActiveObject(selection);
        setSelectedLayer(null);
      } else {
        setSelectedLayer(null);
      }
      editor?.canvas.renderAll();
      return;

    } else if (event.shiftKey && selectedLayers.length > 0) {
      // Shift + 点击：范围选择
      const lastSelectedIndex = objects.findIndex(obj =>
        (obj as any).id === selectedLayers[selectedLayers.length - 1]
      );
      const currentIndex = objects.findIndex(obj => (obj as any).id === layerId);

      const startIndex = Math.min(lastSelectedIndex, currentIndex);
      const endIndex = Math.max(lastSelectedIndex, currentIndex);

      const rangeIds = objects.slice(startIndex, endIndex + 1)
        .filter(obj => obj.name !== "clip")
        .map(obj => (obj as any).id || `layer_${objects.indexOf(obj)}`);

      setSelectedLayers(rangeIds);

      const selectedObjects = rangeIds.map(id =>
        objects.find(obj => (obj as any).id === id)
      ).filter(Boolean) as fabric.Object[];

      editor?.canvas.discardActiveObject();
      if (selectedObjects.length > 1) {
        const selection = new fabric.ActiveSelection(selectedObjects, {
          canvas: editor.canvas
        });
        editor?.canvas.setActiveObject(selection);
      }
      setSelectedLayer(null);
      editor?.canvas.renderAll();
      return;

    } else {
      // 普通点击：单选
      setSelectedLayers([layerId]);
      setSelectedLayer(layerId);

      editor?.canvas.discardActiveObject();
      editor?.canvas.setActiveObject(targetObject);
    }

    editor?.canvas.renderAll();
  };

  // 处理重命名
  const handleRename = (layerId: string, newName: string) => {
    if (newName.trim() === '') return;

    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => (obj as any).id === layerId);

    if (targetObject) {
      (targetObject as any).name = newName.trim();
      updateLayersFromCanvas();
      editor?.canvas.renderAll();
    }

    setEditingLayer(null);
  };

  // 开始重命名
  const startRename = (layerId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setEditingLayer(layerId);
  };

  // 解除分组
  const ungroupLayers = (groupId: string) => {
    if (!editor?.canvas) return;

    const objects = editor.canvas.getObjects() || [];
    const groupObjects = objects.filter(obj => (obj as any).groupId === groupId);

    // 移除分组标记
    groupObjects.forEach(obj => {
      delete (obj as any).groupId;
    });

    // 更新图层列表
    updateLayersFromCanvas();

    console.log(`解除分组: ${groupId}`);
  };

  // 处理右键菜单
  const handleContextMenu = (e: React.MouseEvent, layerId: string, isGroup: boolean) => {
    e.preventDefault();
    e.stopPropagation();
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      layerId,
      isGroup
    });
  };

  // 关闭右键菜单
  const closeContextMenu = () => {
    setContextMenu(null);
  };

  // 更新画布中的对象顺序
  const updateCanvasOrder = (layerList: LayerItem[]) => {
    if (!editor?.canvas) return;

    const objects = editor.canvas.getObjects();
    const workspace = objects.find(obj => obj.name === "clip");

    // 收集所有图层ID（包括分组内的）
    const collectLayerIds = (layers: LayerItem[]): string[] => {
      const ids: string[] = [];
      layers.forEach(layer => {
        if (layer.type === 'group' && layer.children) {
          // 分组内的图层按倒序添加（因为图层列表是倒序显示的）
          ids.push(...layer.children.map(child => child.id).reverse());
        } else {
          ids.push(layer.id);
        }
      });
      return ids;
    };

    const orderedIds = collectLayerIds(layerList.slice().reverse()); // 倒序处理

    // 重新排列画布对象
    const orderedObjects: fabric.Object[] = [];
    if (workspace) orderedObjects.push(workspace);

    orderedIds.forEach(id => {
      const obj = objects.find(obj => (obj as any).id === id);
      if (obj && obj !== workspace) {
        orderedObjects.push(obj);
      }
    });

    // 清空画布并重新添加对象
    editor.canvas.clear();
    orderedObjects.forEach(obj => editor.canvas.add(obj));
    editor.canvas.renderAll();
  };

  // 更新图层列表
  const updateLayersFromCanvas = useCallback(() => {
    if (!editor?.canvas) return;

    console.log('更新图层列表，当前画布对象数量:', editor.canvas.getObjects().length);

    const objects = editor.canvas.getObjects();
    const objectLayers = objects
      .filter(obj => obj.name !== "clip") // Exclude workspace
      .map((obj, index) => {
        const objType = obj.type === "i-text" || obj.type === "textbox" ? "text" :
                       obj.type === "image" ? "image" : "shape";

        // 确保对象有唯一的 ID
        if (!(obj as any).id) {
          (obj as any).id = `layer_${Date.now()}_${index}`;
        }

        const layer = {
          id: (obj as any).id,
          name: (obj as any).name || `${objType} ${index + 1}`,
          type: objType as 'image' | 'text' | 'shape' | 'group',
          visible: obj.visible !== false,
          locked: !obj.selectable,
          isShapeMask: (obj as any).isShapeMask || false,
          maskReference: (obj as any).maskReference,
          groupId: (obj as any).groupId
        };

        console.log('处理图层:', layer);
        return layer;
      });

    // 构建分组结构
    const groups: { [key: string]: LayerItem } = {};
    const ungroupedLayers: LayerItem[] = [];

    // 首先创建所有分组
    objectLayers.forEach(layer => {
      if (layer.groupId && !groups[layer.groupId]) {
        groups[layer.groupId] = {
          id: layer.groupId,
          name: `分组 ${Object.keys(groups).length + 1}`,
          type: 'group',
          visible: true,
          locked: false,
          expanded: true,
          children: []
        };
      }
    });

    // 然后将对象分配到分组或未分组列表，保持原始顺序
    objectLayers.forEach(layer => {
      if (layer.groupId && groups[layer.groupId]) {
        groups[layer.groupId].children!.push(layer);
      } else {
        ungroupedLayers.push(layer);
      }
    });

    // 对每个分组内的子项按画布顺序排序（倒序显示，最上层的在前面）
    Object.values(groups).forEach(group => {
      if (group.children) {
        group.children.sort((a, b) => {
          const aIndex = objects.findIndex(obj => (obj as any).id === a.id);
          const bIndex = objects.findIndex(obj => (obj as any).id === b.id);
          return bIndex - aIndex; // 倒序：画布中索引高的（上层）在图层列表前面
        });
      }
    });

    // 合并分组和未分组的图层，按画布顺序排序
    const allLayers = [...Object.values(groups), ...ungroupedLayers];

    // 按画布中的最高层级对象排序
    const finalLayers = allLayers.sort((a, b) => {
      const getTopIndex = (layer: LayerItem) => {
        if (layer.type === 'group' && layer.children && layer.children.length > 0) {
          // 分组的顺序由其最上层的子对象决定
          return Math.max(...layer.children.map(child =>
            objects.findIndex(obj => (obj as any).id === child.id)
          ));
        } else {
          return objects.findIndex(obj => (obj as any).id === layer.id);
        }
      };

      return getTopIndex(b) - getTopIndex(a); // 倒序，最上层的在前面
    });

    setLayers(finalLayers);
  }, [editor]);

  // Get layers from canvas objects
  useEffect(() => {
    if (!editor?.canvas) return;

    // Initial load
    updateLayersFromCanvas();

    // Listen for canvas changes
    const handleCanvasChange = () => {
      updateLayersFromCanvas();
    };

    const handleSelectionChange = () => {
      const activeObject = editor.canvas.getActiveObject();
      if (activeObject) {
        const objects = editor.canvas.getObjects();

        if (activeObject.type === 'activeSelection') {
          // 处理多选情况
          const selection = activeObject as fabric.ActiveSelection;
          const selectedObjects = selection.getObjects();
          const selectedIds = selectedObjects.map(obj =>
            (obj as any).id || `layer_${objects.indexOf(obj)}`
          );

          setSelectedLayers(selectedIds);
          setSelectedLayer(selectedIds.length > 0 ? selectedIds[0] : null);
        } else {
          // 处理单选情况
          const objId = (activeObject as any).id || `layer_${objects.indexOf(activeObject)}`;
          setSelectedLayer(objId);
          setSelectedLayers([objId]);
        }
      } else {
        setSelectedLayer(null);
        setSelectedLayers([]);
      }
      updateLayersFromCanvas();
    };

    editor.canvas.on('object:added', (e) => {
      handleCanvasChange();
      // 当添加新对象时，检查是否需要应用现有的蒙版
      const addedObject = e.target;
      if (addedObject && addedObject.name !== "clip" && !(addedObject as any).isShapeMask) {
        console.log('新对象添加，立即检查蒙版应用:', addedObject.type);

        // 使用 requestAnimationFrame 确保对象完全添加后再应用蒙版
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            console.log('执行蒙版更新...');
            updateAllMasks();
          });
        });
      }
    });
    editor.canvas.on('object:removed', (e) => {
      handleCanvasChange();
      // 如果删除的是蒙版对象，清除相关的蒙版效果
      const removedObject = e.target;
      if (removedObject && (removedObject as any).isShapeMask) {
        const maskId = (removedObject as any).id || `layer_${editor.canvas.getObjects().indexOf(removedObject)}`;
        console.log('蒙版对象被删除，清除相关效果:', maskId);

        // 清除所有被此蒙版影响的对象
        const allObjects = editor.canvas.getObjects();
        allObjects.forEach(obj => {
          if ((obj as any).maskReference === maskId) {
            obj.set('clipPath', null);
            delete (obj as any).maskReference;
            console.log('清除对象的蒙版效果:', obj.type);
          }
        });

        editor.canvas.renderAll();
      }
    });
    editor.canvas.on('object:modified', handleCanvasChange);
    // 监听对象移动事件
    editor.canvas.on('object:moving', (e) => {
      handleCanvasChange();
      const target = e.target as any;

      // 如果移动的是蒙版对象或被蒙版的对象，都需要更新蒙版
      if (target.isShapeMask || target.maskReference) {
        scheduleUpdateMasks();
      }
    });

    // 监听对象缩放事件
    editor.canvas.on('object:scaling', (e) => {
      handleCanvasChange();
      const target = e.target as any;

      if (target.isShapeMask || target.maskReference) {
        scheduleUpdateMasks();
      }
    });

    // 监听对象旋转事件
    editor.canvas.on('object:rotating', (e) => {
      handleCanvasChange();
      const target = e.target as any;

      if (target.isShapeMask || target.maskReference) {
        scheduleUpdateMasks();
      }
    });

    // 监听对象变换完成事件
    editor.canvas.on('object:modified', (e) => {
      handleCanvasChange();
      const target = e.target as any;

      if (target && ((target as any).isShapeMask || (target as any).maskReference)) {
        scheduleUpdateMasks();
      }
    });
    editor.canvas.on('selection:created', handleSelectionChange);
    editor.canvas.on('selection:updated', handleSelectionChange);
    editor.canvas.on('selection:cleared', () => {
      setSelectedLayer(null);
      setSelectedLayers([]); // 清除多选状态
      updateLayersFromCanvas();
    });

    // 监听撤销/重做后的图层更新事件
    const handleHistoryUpdate = () => {
      console.log('检测到撤销/重做操作，更新图层列表');
      // 清除当前选择状态
      setSelectedLayer(null);
      setSelectedLayers([]);
      // 强制更新图层列表
      setTimeout(() => {
        updateLayersFromCanvas();
      }, 50);
    };

    editor.canvas.on('path:created', handleHistoryUpdate);
    editor.canvas.on('object:added', handleHistoryUpdate);

    return () => {
      editor.canvas.off('object:added', handleCanvasChange);
      editor.canvas.off('object:removed', handleCanvasChange);
      editor.canvas.off('object:modified', handleCanvasChange);
      editor.canvas.off('object:moving', handleCanvasChange);
      editor.canvas.off('object:scaling', handleCanvasChange);
      editor.canvas.off('object:rotating', handleCanvasChange);
      editor.canvas.off('selection:created', handleSelectionChange);
      editor.canvas.off('selection:updated', handleSelectionChange);
      editor.canvas.off('selection:cleared', handleSelectionChange);
      editor.canvas.off('path:created', handleHistoryUpdate);
      editor.canvas.off('object:added', handleHistoryUpdate);

      // 清理防抖的蒙版更新
      if (debouncedUpdateMasks.current) {
        cancelAnimationFrame(debouncedUpdateMasks.current);
      }
    };
  }, [editor]);

  const toggleVisibility = (layerId: string) => {
    const updateLayer = (items: LayerItem[]): LayerItem[] => {
      return items.map(item => {
        if (item.id === layerId) {
          const newVisibility = !item.visible;
          // 如果是分组，同时更新所有子项的可见性
          if (item.children) {
            const updatedChildren = item.children.map(child => ({
              ...child,
              visible: newVisibility
            }));
            return { ...item, visible: newVisibility, children: updatedChildren };
          }
          return { ...item, visible: newVisibility };
        }
        if (item.children) {
          return { ...item, children: updateLayer(item.children) };
        }
        return item;
      });
    };
    setLayers(updateLayer(layers));

    // Find and toggle the actual canvas object(s)
    const objects = editor?.canvas.getObjects() || [];

    // 查找目标对象
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject) {
      const newVisibility = !targetObject.visible;
      targetObject.set('visible', newVisibility);

      // 如果是分组，同时切换分组内所有对象的可见性
      const groupId = (targetObject as any).groupId;
      if (groupId) {
        objects.forEach(obj => {
          if ((obj as any).groupId === groupId) {
            obj.set('visible', newVisibility);
          }
        });
      }

      // 如果是蒙版对象，需要更新蒙版效果
      if ((targetObject as any).isShapeMask) {
        if (newVisibility) {
          // 蒙版显示时，重新应用蒙版效果
          setTimeout(() => {
            updateAllMasks();
          }, 50);
        } else {
          // 蒙版隐藏时，清除蒙版效果
          const maskId = (targetObject as any).id || `layer_${objects.indexOf(targetObject)}`;
          objects.forEach(obj => {
            if ((obj as any).maskReference === maskId) {
              obj.set('clipPath', null);
              console.log('蒙版隐藏，清除对象的蒙版效果:', obj.type);
            }
          });
        }
      }

      editor?.canvas.renderAll();
    }
  };

  const toggleLock = (layerId: string) => {
    const updateLayer = (items: LayerItem[]): LayerItem[] => {
      return items.map(item => {
        if (item.id === layerId) {
          const newLocked = !item.locked;
          // 如果是分组，同时更新所有子项的锁定状态
          if (item.children) {
            const updatedChildren = item.children.map(child => ({
              ...child,
              locked: newLocked
            }));
            return { ...item, locked: newLocked, children: updatedChildren };
          }
          return { ...item, locked: newLocked };
        }
        if (item.children) {
          return { ...item, children: updateLayer(item.children) };
        }
        return item;
      });
    };
    setLayers(updateLayer(layers));

    // Find and toggle the actual canvas object(s)
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject) {
      const isLocked = !targetObject.selectable;
      targetObject.set({
        selectable: isLocked,
        evented: isLocked,
        hoverCursor: isLocked ? 'default' : 'move',
        moveCursor: isLocked ? 'default' : 'move'
      });

      // 如果是分组，同时切换分组内所有对象的锁定状态
      const groupId = (targetObject as any).groupId;
      if (groupId) {
        objects.forEach(obj => {
          if ((obj as any).groupId === groupId) {
            obj.set({
              selectable: isLocked,
              evented: isLocked,
              hoverCursor: isLocked ? 'default' : 'move',
              moveCursor: isLocked ? 'default' : 'move'
            });
          }
        });
      }

      // 如果当前对象被选中且正在被锁定，取消选择
      if (!isLocked && editor?.canvas.getActiveObject() === targetObject) {
        editor?.canvas.discardActiveObject();
      }

      editor?.canvas.renderAll();
    }
  };

  const toggleExpanded = (layerId: string) => {
    const updateLayer = (items: LayerItem[]): LayerItem[] => {
      return items.map(item => {
        if (item.id === layerId && item.type === 'group') {
          return { ...item, expanded: !item.expanded };
        }
        if (item.children) {
          return { ...item, children: updateLayer(item.children) };
        }
        return item;
      });
    };
    setLayers(updateLayer(layers));
  };



  const deleteLayer = (layerId: string) => {
    const removeLayer = (items: LayerItem[]): LayerItem[] => {
      return items.filter(item => {
        if (item.id === layerId) {
          return false;
        }
        if (item.children) {
          item.children = removeLayer(item.children);
        }
        return true;
      });
    };
    setLayers(removeLayer(layers));

    // Find and delete the actual canvas object
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject && targetObject.name !== "clip") {
      editor?.canvas.remove(targetObject);
      editor?.canvas.renderAll();
    }
  };

  // Right-click menu actions
  const duplicateLayer = (layerId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject) {
      targetObject.clone((cloned: fabric.Object) => {
        cloned.set({
          left: (cloned.left || 0) + 10,
          top: (cloned.top || 0) + 10,
        });
        editor?.canvas.add(cloned);
        editor?.canvas.setActiveObject(cloned);
        editor?.canvas.renderAll();
      });
    }
  };

  const moveLayerUp = (layerId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject) {
      editor?.canvas.setActiveObject(targetObject);
      editor?.bringForward();
    }
  };

  const moveLayerDown = (layerId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject) {
      editor?.canvas.setActiveObject(targetObject);
      editor?.sendBackwards();
    }
  };

  const moveToTop = (layerId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject) {
      editor?.canvas.bringToFront(targetObject);
      editor?.canvas.renderAll();
    }
  };

  const moveToBottom = (layerId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const targetObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (targetObject) {
      editor?.canvas.sendToBack(targetObject);
      editor?.canvas.renderAll();
    }
  };



  // Test clipPath functionality
  const testClipPath = () => {
    if (!editor?.canvas) return;

    // Clear canvas first (except workspace)
    const workspace = editor.canvas.getObjects().find(obj => obj.name === "clip");
    editor.canvas.clear();
    if (workspace) {
      editor.canvas.add(workspace);
    }

    // Create a large blue rectangle as the base shape
    const rect = new fabric.Rect({
      left: 300,
      top: 250,
      width: 200,
      height: 150,
      fill: 'blue',
      name: 'test-rect'
    });

    // Create a smaller red circle as mask
    const circle = new fabric.Circle({
      radius: 60,
      left: 350,
      top: 275,
      fill: 'red',
      name: 'test-circle-mask'
    });

    // 设置对象ID
    (circle as any).id = 'test_circle_mask';
    (rect as any).id = 'test_rect_target';

    // Add both objects to canvas
    editor.canvas.add(circle);  // Add mask first (lower layer)
    editor.canvas.add(rect);    // Add shape to be masked (upper layer)

    editor.canvas.renderAll();
    console.log('Test shapes created - red circle (mask) and blue rectangle (to be masked)');
    console.log('Circle ID:', (circle as any).id, 'Rect ID:', (rect as any).id);
    console.log('Now select the red circle and click "设为蒙版" to test masking');
  };

  // 测试分组蒙版功能
  const testGroupMask = () => {
    if (!editor?.canvas) return;

    // 清除现有对象
    const workspace = editor.canvas.getObjects().find(obj => obj.name === "clip");
    editor.canvas.clear();
    if (workspace) {
      editor.canvas.add(workspace);
    }

    // 创建分组1的对象
    const star = new fabric.Polygon([
      {x: 0, y: -50}, {x: 14, y: -20}, {x: 47, y: -15}, {x: 23, y: 7},
      {x: 29, y: 40}, {x: 0, y: 25}, {x: -29, y: 40}, {x: -23, y: 7},
      {x: -47, y: -15}, {x: -14, y: -20}
    ], {
      left: 200,
      top: 200,
      fill: 'pink',
      originX: 'center',
      originY: 'center'
    });

    const rect1 = new fabric.Rect({
      width: 80,
      height: 60,
      fill: 'orange',
      left: 180,
      top: 180
    });

    const rect2 = new fabric.Rect({
      width: 70,
      height: 50,
      fill: 'lightblue',
      left: 220,
      top: 220
    });

    // 创建分组外的对象
    const rect3 = new fabric.Rect({
      width: 100,
      height: 80,
      fill: 'green',
      left: 350,
      top: 150
    });

    // 设置ID和名称
    (star as any).id = 'star_1';
    (star as any).name = '星形 1';
    (rect1 as any).id = 'rect_1';
    (rect1 as any).name = '矩形 1';
    (rect2 as any).id = 'rect_2';
    (rect2 as any).name = '矩形 2';
    (rect3 as any).id = 'rect_3';
    (rect3 as any).name = '矩形 3';

    // 设置分组1
    const groupId = 'group_test_1';
    (star as any).groupId = groupId;
    (rect1 as any).groupId = groupId;
    (rect2 as any).groupId = groupId;
    // rect3 不设置 groupId，保持在分组外

    // 添加到画布
    editor.canvas.add(star);
    editor.canvas.add(rect1);
    editor.canvas.add(rect2);
    editor.canvas.add(rect3);

    editor.canvas.renderAll();
    updateLayersFromCanvas();

    console.log('测试场景创建完成:');
    console.log('- 分组1: 星形1(蒙版), 矩形1, 矩形2');
    console.log('- 分组外: 矩形3');
    console.log('选择星形1并设为蒙版，应该只影响同组的矩形1和矩形2');
  };

  // 蒙版组管理
  const createMaskGroup = useCallback((maskObject: fabric.Object, maskedObjects: fabric.Object[]) => {
    if (!editor?.canvas) return null;

    // 创建一个组来管理蒙版关系
    const group = new fabric.Group([maskObject, ...maskedObjects], {
      selectable: false,
      evented: false,
      excludeFromExport: false
    });

    // 标记为蒙版组
    (group as any).isMaskGroup = true;
    (group as any).maskObject = maskObject;
    (group as any).maskedObjects = maskedObjects;

    return group;
  }, [editor]);

  // 应用蒙版效果 - 使用绝对定位方法，优化性能
  const applyMaskEffect = useCallback((maskObject: fabric.Object, targetObject: fabric.Object) => {
    // 创建蒙版克隆
    maskObject.clone((maskClone: fabric.Object) => {
      // 使用绝对定位，让蒙版克隆与原蒙版对象完全一致
      maskClone.set({
        left: maskObject.left,
        top: maskObject.top,
        originX: maskObject.originX,
        originY: maskObject.originY,
        scaleX: maskObject.scaleX || 1,
        scaleY: maskObject.scaleY || 1,
        angle: maskObject.angle || 0,
        fill: 'black',
        stroke: null,
        strokeWidth: 0,
        opacity: 1,
        absolutePositioned: true  // 关键：使用绝对定位
      });

      // 应用clipPath，不立即渲染
      targetObject.set('clipPath', maskClone);

      console.log('应用蒙版效果:', {
        mask: maskObject.type,
        target: targetObject.type,
        maskPos: { left: maskObject.left, top: maskObject.top },
        targetPos: { left: targetObject.left, top: targetObject.top },
        absolutePositioned: true
      });
    });
  }, []);

  // 更新所有蒙版关系
  const updateAllMasks = useCallback(() => {
    if (!editor?.canvas) return;

    console.log('更新所有蒙版关系...');
    const objects = editor.canvas.getObjects();

    // 找到所有蒙版对象
    const maskObjects = objects.filter(obj => (obj as any).isShapeMask);
    console.log('找到蒙版对象:', maskObjects.length, maskObjects.map(m => m.type));

    // 首先清除所有现有的蒙版引用
    objects.forEach(obj => {
      if ((obj as any).maskReference) {
        delete (obj as any).maskReference;
        obj.set('clipPath', null);
      }
    });

    // 批量处理所有蒙版
    let needsRender = false;
    maskObjects.forEach(maskObject => {
      const maskId = (maskObject as any).id || `layer_${objects.indexOf(maskObject)}`;
      const maskGroupId = (maskObject as any).groupId;
      const maskIndex = objects.indexOf(maskObject);

      console.log(`处理蒙版 ${maskId}, 分组: ${maskGroupId || '无'}, 索引: ${maskIndex}`);

      // 重新计算应该被此蒙版影响的对象（基于分组和层级）
      const objectsToMask = objects.filter((obj, index) => {
        // 排除蒙版对象本身、工作区和其他蒙版对象
        if (obj === maskObject || obj.name === "clip" || (obj as any).isShapeMask) {
          return false;
        }

        // 只影响在蒙版对象上方的对象（更高的索引）
        if (index <= maskIndex) {
          return false;
        }

        // 如果蒙版对象在分组中，只影响同一分组的对象
        if (maskGroupId) {
          return (obj as any).groupId === maskGroupId;
        }

        // 如果蒙版对象不在分组中，影响所有不在分组中的对象
        return !(obj as any).groupId;
      });

      console.log(`蒙版 ${maskId} 将影响 ${objectsToMask.length} 个对象:`,
        objectsToMask.map(o => `${o.type}(${(o as any).id || 'no-id'})`));

      // 为符合条件的对象设置蒙版引用并应用效果
      objectsToMask.forEach(obj => {
        (obj as any).maskReference = maskId;
        applyMaskEffect(maskObject, obj);
        needsRender = true;
      });
    });

    // 只在最后统一渲染一次
    if (needsRender) {
      editor.canvas.renderAll();
    }
    console.log('蒙版更新完成');
  }, [editor, applyMaskEffect]);

  // 使用 requestAnimationFrame 优化蒙版更新性能
  const scheduleUpdateMasks = useCallback(() => {
    if (debouncedUpdateMasks.current) {
      cancelAnimationFrame(debouncedUpdateMasks.current);
    }
    debouncedUpdateMasks.current = requestAnimationFrame(() => {
      updateAllMasks();
    });
  }, [updateAllMasks]);



  // 设置形状蒙版
  const setAsShapeMask = (layerId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const maskObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (!maskObject) {
      console.error('找不到蒙版对象:', layerId);
      return;
    }

    console.log('设置蒙版对象:', maskObject.type, layerId);

    // 保存原始样式
    const originalStyle = {
      fill: maskObject.fill,
      stroke: maskObject.stroke,
      strokeWidth: maskObject.strokeWidth,
      opacity: maskObject.opacity,
      strokeDashArray: maskObject.strokeDashArray
    };

    // 标记为蒙版对象，保存原始样式
    (maskObject as any).isShapeMask = true;
    (maskObject as any).originalStyle = originalStyle;
    (maskObject as any).name = (maskObject as any).name + " (蒙版)";

    // 获取蒙版对象的分组信息
    const maskGroupId = (maskObject as any).groupId;
    const maskIndex = objects.indexOf(maskObject);

    // 如果蒙版对象在分组中，只影响同一分组内在其上方的对象
    // 如果蒙版对象不在分组中，影响所有在其上方的对象
    const objectsToMask = objects.filter((obj, index) => {
      // 排除蒙版对象本身、工作区和其他蒙版对象
      if (obj === maskObject || obj.name === "clip" || (obj as any).isShapeMask) {
        return false;
      }

      // 只影响在蒙版对象上方的对象（更高的索引）
      if (index <= maskIndex) {
        return false;
      }

      // 如果蒙版对象在分组中，只影响同一分组的对象
      if (maskGroupId) {
        return (obj as any).groupId === maskGroupId;
      }

      // 如果蒙版对象不在分组中，影响所有不在分组中的对象
      return !(obj as any).groupId;
    });

    console.log('设置蒙版:', maskObject.type, '影响对象:', objectsToMask.map(o => o.type));

    if (objectsToMask.length > 0) {
      // 为每个对象设置蒙版引用
      objectsToMask.forEach(obj => {
        (obj as any).maskReference = layerId;
      });

      // 立即应用蒙版，不使用防抖
      console.log('立即应用蒙版...');
      updateAllMasks();
    }

    editor?.canvas.renderAll();

    // 触发图层列表重新渲染
    const event = new Event('object:modified');
    editor?.canvas.fire('object:modified', { target: maskObject });
  };

  const removeShapeMask = (layerId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const maskObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === layerId;
    });

    if (maskObject && (maskObject as any).isShapeMask) {
      console.log('Removing mask from:', maskObject.type);

      // 恢复原始样式
      const originalStyle = (maskObject as any).originalStyle;
      if (originalStyle) {
        maskObject.set(originalStyle);
      }

      // Remove shape mask flag
      delete (maskObject as any).isShapeMask;
      delete (maskObject as any).originalStyle;
      (maskObject as any).name = (maskObject as any).name.replace(" (蒙版)", "");

      // Remove clipping path from objects that were masked by this specific mask
      const allObjects = editor?.canvas.getObjects() || [];
      allObjects.forEach(obj => {
        if ((obj as any).maskReference === layerId) {
          obj.set('clipPath', null);
          delete (obj as any).maskReference;
          console.log('Removed mask from:', obj.type);
        }
      });

      editor?.canvas.renderAll();

      // Trigger re-render of layers list
      const event = new Event('object:modified');
      editor?.canvas.fire('object:modified', { target: maskObject });
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, layerId: string) => {
    e.stopPropagation(); // 防止事件冒泡
    setDraggedLayer(layerId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', layerId);
    console.log('开始拖拽:', layerId);
  };

  const handleDragOver = (e: React.DragEvent, layerId: string) => {
    e.preventDefault();
    e.stopPropagation(); // 防止事件冒泡

    // 查找目标图层（可能在分组中）
    const findLayerInHierarchy = (layers: LayerItem[], id: string): LayerItem | null => {
      for (const layer of layers) {
        if (layer.id === id) return layer;
        if (layer.children) {
          const found = findLayerInHierarchy(layer.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    const targetLayer = findLayerInHierarchy(layers, layerId);
    const isTargetGroup = targetLayer?.type === 'group';

    // 为分组设置不同的拖拽效果
    e.dataTransfer.dropEffect = isTargetGroup ? 'copy' : 'move';
    setDragOverLayer(layerId);
  };

  const handleDragLeave = () => {
    setDragOverLayer(null);
  };

  const handleDrop = (e: React.DragEvent, targetLayerId: string) => {
    e.preventDefault();
    e.stopPropagation(); // 防止事件冒泡

    const draggedLayerId = e.dataTransfer.getData('text/plain');

    if (draggedLayerId === targetLayerId) {
      setDraggedLayer(null);
      setDragOverLayer(null);
      return;
    }

    const objects = editor?.canvas.getObjects() || [];
    const draggedObject = objects.find(obj => {
      const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
      return objId === draggedLayerId;
    });

    if (!draggedObject) {
      setDraggedLayer(null);
      setDragOverLayer(null);
      return;
    }

    // 查找目标图层（可能在分组中）
    const findLayerInHierarchy = (layers: LayerItem[], id: string): LayerItem | null => {
      for (const layer of layers) {
        if (layer.id === id) return layer;
        if (layer.children) {
          const found = findLayerInHierarchy(layer.children, id);
          if (found) return found;
        }
      }
      return null;
    };

    const targetLayer = findLayerInHierarchy(layers, targetLayerId);
    const draggedLayer = findLayerInHierarchy(layers, draggedLayerId);

    if (!targetLayer || !draggedLayer) {
      setDraggedLayer(null);
      setDragOverLayer(null);
      return;
    }

    const isTargetGroup = targetLayer.type === 'group';
    const isDraggedMask = (draggedObject as any).isShapeMask;
    const oldGroupId = (draggedObject as any).groupId;

    console.log(`拖拽操作: ${draggedLayerId} -> ${targetLayerId}`);
    console.log(`目标是分组: ${isTargetGroup}, 原分组: ${oldGroupId || '无'}`);

    if (isTargetGroup) {
      // 拖拽到分组：将对象加入分组
      (draggedObject as any).groupId = targetLayerId;
      console.log(`将 ${draggedLayerId} 加入分组 ${targetLayerId}`);
    } else {
      // 拖拽到普通图层：调整顺序并可能改变分组
      const targetObject = objects.find(obj => {
        const objId = (obj as any).id || `layer_${objects.indexOf(obj)}`;
        return objId === targetLayerId;
      });

      if (targetObject) {
        const draggedIndex = objects.indexOf(draggedObject);
        const targetIndex = objects.indexOf(targetObject);
        const targetGroupId = (targetObject as any).groupId;

        console.log(`位置调整: ${draggedIndex} -> ${targetIndex}`);
        console.log(`分组调整: ${oldGroupId || '无'} -> ${targetGroupId || '无'}`);

        // 更新分组归属
        if (targetGroupId) {
          (draggedObject as any).groupId = targetGroupId;
        } else {
          delete (draggedObject as any).groupId;
        }

        // 调整画布中的顺序
        if (draggedIndex !== targetIndex) {
          editor?.canvas.moveTo(draggedObject, targetIndex);
        }
      }
    }

    editor?.canvas.renderAll();

    // 立即更新图层列表
    updateLayersFromCanvas();

    // 如果拖拽的是蒙版对象，延迟更新蒙版效果
    if (isDraggedMask) {
      setTimeout(() => {
        updateAllMasks();
      }, 100);
    }

    setDraggedLayer(null);
    setDragOverLayer(null);
  };




  const getLayerIcon = (type: string) => {
    switch (type) {
      case 'image': return Image;
      case 'text': return Type;
      case 'shape': return Square;
      case 'group': return Folder;
      default: return Square;
    }
  };

  const renderLayer = (layer: LayerItem, depth = 0) => {
    const Icon = getLayerIcon(layer.type);
    const isGroup = layer.type === 'group';

    return (
      <ContextMenu key={layer.id}>
        <ContextMenuTrigger asChild>
          <div
            style={{ marginLeft: depth * 16 }}
            draggable
            onDragStart={(e) => handleDragStart(e, layer.id)}
            onDragOver={(e) => handleDragOver(e, layer.id)}
            onDragLeave={handleDragLeave}
            onDrop={(e) => handleDrop(e, layer.id)}
          >
            <div
              className={cn(
                "flex items-center gap-2 p-2 rounded hover:bg-gray-50 cursor-pointer transition-colors",
                (selectedLayer === layer.id || selectedLayers.includes(layer.id)) && "bg-blue-50 border border-blue-200",
                draggedLayer === layer.id && "opacity-50",
                dragOverLayer === layer.id && (
                  isGroup
                    ? "bg-green-100 border-2 border-green-400 border-dashed" // 分组拖拽目标
                    : "bg-blue-100 border-t-2 border-blue-400" // 普通图层拖拽目标
                )
              )}
              onClick={(e) => handleLayerClick(layer.id, e, layer)}
            >
              <GripVertical className="h-3 w-3 text-gray-400 cursor-grab" />
          {isGroup && (
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-4 w-4"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(layer.id);
              }}
            >
              {layer.expanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
          
          <Icon className={cn(
            "h-4 w-4",
            layer.isShapeMask ? "text-pink-600" : "text-gray-600"
          )} />

          <div className="flex-1 flex items-center gap-2">
            {editingLayer === layer.id ? (
              <input
                type="text"
                defaultValue={layer.name}
                className="text-sm bg-white border border-blue-300 rounded px-1 py-0.5 flex-1"
                autoFocus
                onBlur={(e) => handleRename(layer.id, e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleRename(layer.id, e.currentTarget.value);
                  } else if (e.key === 'Escape') {
                    setEditingLayer(null);
                  }
                }}
                onClick={(e) => e.stopPropagation()}
              />
            ) : (
              <span
                className={cn(
                  "text-sm truncate",
                  layer.isShapeMask && "text-pink-600 font-medium"
                )}
                onDoubleClick={(e) => startRename(layer.id, e)}
              >
                {layer.name}
              </span>
            )}

            {/* 显示蒙版关联关系 */}
            {layer.isShapeMask && (
              <span className="text-xs bg-pink-100 text-pink-700 px-1 py-0.5 rounded">
                蒙版
              </span>
            )}
            {(layer as any).maskReference && (
              <span className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded">
                被蒙版
              </span>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-6 w-6"
            onClick={(e) => {
              e.stopPropagation();
              toggleVisibility(layer.id);
            }}
          >
            {layer.visible ? (
              <Eye className="h-3 w-3" />
            ) : (
              <EyeOff className="h-3 w-3 text-gray-400" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-6 w-6"
            onClick={(e) => {
              e.stopPropagation();
              toggleLock(layer.id);
            }}
          >
            {layer.locked ? (
              <Lock className="h-3 w-3" />
            ) : (
              <Unlock className="h-3 w-3 text-gray-400" />
            )}
          </Button>
          
          {isGroup && (
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-6 w-6 text-blue-500 hover:text-blue-700"
              onClick={(e) => {
                e.stopPropagation();
                ungroupLayers(layer.id);
              }}
              title="解除分组"
            >
              <FolderOpen className="h-3 w-3" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-6 w-6 text-red-500 hover:text-red-700"
            onClick={(e) => {
              e.stopPropagation();
              deleteLayer(layer.id);
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
        
            {isGroup && layer.expanded && layer.children && (
              <div>
                {layer.children.map(child => renderLayer(child, depth + 1))}
              </div>
            )}
          </div>
        </ContextMenuTrigger>

        <ContextMenuContent className="w-64">
          <ContextMenuItem onClick={() => duplicateLayer(layer.id)}>
            <Copy className="mr-2 h-4 w-4" />
            复制
            <ContextMenuShortcut>Ctrl+D</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuItem onClick={() => startRename(layer.id, { stopPropagation: () => {} } as any)}>
            <Edit className="mr-2 h-4 w-4" />
            重命名
            <ContextMenuShortcut>F2</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuSeparator />

          <ContextMenuItem onClick={() => moveToTop(layer.id)}>
            <ArrowUp className="mr-2 h-4 w-4" />
            移至顶层
            <ContextMenuShortcut>Ctrl+]</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuItem onClick={() => moveLayerUp(layer.id)}>
            <ArrowUp className="mr-2 h-4 w-4" />
            向上移动一层
            <ContextMenuShortcut>Ctrl+Shift+]</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuItem onClick={() => moveLayerDown(layer.id)}>
            <ArrowDown className="mr-2 h-4 w-4" />
            向下移动一层
            <ContextMenuShortcut>Ctrl+Shift+[</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuItem onClick={() => moveToBottom(layer.id)}>
            <ArrowDown className="mr-2 h-4 w-4" />
            移至底层
            <ContextMenuShortcut>Ctrl+[</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuSeparator />

          {isGroup ? (
            <ContextMenuItem onClick={() => ungroupLayers(layer.id)}>
              <FolderOpen className="mr-2 h-4 w-4" />
              取消分组
              <ContextMenuShortcut>Ctrl+Shift+G</ContextMenuShortcut>
            </ContextMenuItem>
          ) : (
            <ContextMenuItem onClick={() => createGroup()}>
              <Group className="mr-2 h-4 w-4" />
              创建分组
              <ContextMenuShortcut>Ctrl+G</ContextMenuShortcut>
            </ContextMenuItem>
          )}

          <ContextMenuSeparator />

          {/* Shape mask options */}
          {!layer.isShapeMask ? (
            <ContextMenuItem onClick={() => setAsShapeMask(layer.id)}>
              <Square className="mr-2 h-4 w-4" />
              设置为形状蒙版
            </ContextMenuItem>
          ) : (
            <ContextMenuItem onClick={() => removeShapeMask(layer.id)}>
              <Square className="mr-2 h-4 w-4" />
              取消形状蒙版
            </ContextMenuItem>
          )}

          <ContextMenuSeparator />

          <ContextMenuItem onClick={() => toggleVisibility(layer.id)}>
            {layer.visible ? (
              <>
                <EyeOff className="mr-2 h-4 w-4" />
                隐藏
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                显示
              </>
            )}
            <ContextMenuShortcut>Ctrl+;</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuItem onClick={() => toggleLock(layer.id)}>
            {layer.locked ? (
              <>
                <Unlock className="mr-2 h-4 w-4" />
                解锁
              </>
            ) : (
              <>
                <Lock className="mr-2 h-4 w-4" />
                锁定
              </>
            )}
            <ContextMenuShortcut>Ctrl+L</ContextMenuShortcut>
          </ContextMenuItem>

          <ContextMenuSeparator />

          <ContextMenuItem
            onClick={() => deleteLayer(layer.id)}
            className="text-red-600 focus:text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            删除
            <ContextMenuShortcut>Delete</ContextMenuShortcut>
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
    );
  };

  const createGroup = () => {
    const selectedObjects = editor?.canvas.getActiveObjects() || [];

    if (selectedObjects.length > 1) {
      // 为选中的对象创建逻辑分组（不使用 Fabric.js 的 Group）
      const groupId = `group_${Date.now()}`;
      const groupName = `分组 ${layers.filter(l => l.type === 'group').length + 1}`;

      // 为每个选中对象设置分组ID
      selectedObjects.forEach(obj => {
        (obj as any).groupId = groupId;
      });

      console.log(`创建分组 ${groupName}，包含 ${selectedObjects.length} 个对象`);

      // 更新图层列表
      updateLayersFromCanvas();

      // 清除选择
      editor?.canvas.discardActiveObject();
      editor?.canvas.renderAll();
    } else {
      console.log('请选择至少两个对象来创建分组');
    }
  };

  const ungroupObjects = (groupId: string) => {
    const objects = editor?.canvas.getObjects() || [];
    const groupObject = objects.find(obj => (obj as any).id === groupId);

    if (groupObject && groupObject.type === 'group') {
      const group = groupObject as fabric.Group;
      const items = group.getObjects();

      // Ungroup
      group.destroy();
      editor?.canvas.remove(group);

      // Add individual objects back
      items.forEach(item => {
        editor?.canvas.add(item);
      });

      editor?.canvas.renderAll();
    }
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "layers" ? "visible" : "hidden"
      )}
    >
      <ToolSidebarHeader
        title="Layers"
        description="Manage canvas layers"
      />

      <div className="p-3 border-b space-y-2">
        <Button
          onClick={testClipPath}
          variant="outline"
          size="sm"
          className="w-full mb-2"
        >
          测试蒙版功能
        </Button>
        <Button
          onClick={testGroupMask}
          variant="outline"
          size="sm"
          className="w-full mb-2"
        >
          测试分组蒙版
        </Button>
        <Button
          onClick={createGroup}
          variant="outline"
          size="sm"
          className="w-full"
        >
          <Plus className="h-4 w-4 mr-2" />
          {editor?.canvas.getActiveObjects().length > 1 ? "Group Selected" : "Create Group"}
        </Button>

        <div className="text-xs text-muted-foreground text-center">
          {editor?.canvas.getActiveObjects().length > 1
            ? "Multiple objects selected - click to group them"
            : "Select multiple objects to group them"
          }
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-2 space-y-1">
          {layers.map(layer => renderLayer(layer))}
        </div>
      </ScrollArea>

      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
