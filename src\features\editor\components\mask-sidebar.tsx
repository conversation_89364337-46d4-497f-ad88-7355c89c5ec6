import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { ActiveTool, Editor } from "@/features/editor/types";
import { ToolSidebarClose } from "@/features/editor/components/tool-sidebar-close";
import { ToolSidebarHeader } from "@/features/editor/components/tool-sidebar-header";
import { cn } from "@/lib/utils";
import { Circle, Square, Triangle, Heart, Star, Hexagon } from "lucide-react";

interface MaskSidebarProps {
  editor: Editor | undefined;
  activeTool: ActiveTool;
  onChangeActiveTool: (tool: ActiveTool) => void;
}

const maskShapes = [
  { name: "Circle", type: "circle", icon: Circle },
  { name: "Square", type: "square", icon: Square },
  { name: "Triangle", type: "triangle", icon: Triangle },
  { name: "Heart", type: "heart", icon: Heart },
  { name: "Star", type: "star", icon: Star },
  { name: "Hexagon", type: "hexagon", icon: Hexagon },
];

export const MaskSidebar = ({
  editor,
  activeTool,
  onChangeActiveTool,
}: MaskSidebarProps) => {
  const onClose = () => {
    onChangeActiveTool("select");
  };

  const applyMask = (maskType: string) => {
    editor?.applyImageMask(maskType);
    onClose();
  };

  const removeMask = () => {
    editor?.removeImageMask();
  };

  return (
    <aside
      className={cn(
        "bg-white relative border-r z-[40] w-[360px] h-full flex flex-col",
        activeTool === "mask" ? "visible" : "hidden"
      )}
    >
      <ToolSidebarHeader
        title="Shape Mask"
        description="Apply shape masks to images"
      />

      <div className="p-4 space-y-4">
        <div>
          <Label className="text-sm font-medium mb-3 block">
            Select Shape
          </Label>
          <div className="grid grid-cols-2 gap-3">
            {maskShapes.map((shape) => {
              const Icon = shape.icon;
              return (
                <Button
                  key={shape.type}
                  variant="outline"
                  className="h-20 flex flex-col gap-2 hover:bg-blue-50 hover:border-blue-300"
                  onClick={() => applyMask(shape.type)}
                >
                  <Icon className="size-6" />
                  <span className="text-xs font-medium">{shape.name}</span>
                </Button>
              );
            })}
          </div>
        </div>

        <div className="pt-4 border-t space-y-2">
          <Button
            onClick={removeMask}
            variant="outline"
            className="w-full"
            size="lg"
          >
            Remove Mask
          </Button>
          
          <div className="text-xs text-muted-foreground text-center">
            Click a shape to apply it as a mask to the selected image
          </div>
        </div>

        <div className="pt-4 border-t">
          <Label className="text-sm font-medium mb-2 block">
            Custom Masks
          </Label>
          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => applyMask("rounded-square")}
            >
              Rounded Square
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => applyMask("oval")}
            >
              Oval
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => applyMask("diamond")}
            >
              Diamond
            </Button>
          </div>
        </div>
      </div>

      <ToolSidebarClose onClick={onClose} />
    </aside>
  );
};
