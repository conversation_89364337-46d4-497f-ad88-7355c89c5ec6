"use client";

import { useEffect, useState } from "react";
import { Check, Loader2, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface SaveStatusProps {
  isSaving?: boolean;
  lastSaved?: Date | null;
  hasError?: boolean;
}

export const SaveStatus = ({ isSaving = false, lastSaved, hasError = false }: SaveStatusProps) => {
  const [showSaved, setShowSaved] = useState(false);

  useEffect(() => {
    if (!isSaving && lastSaved && !hasError) {
      setShowSaved(true);
      const timer = setTimeout(() => {
        setShowSaved(false);
      }, 2000); // 显示2秒后淡出

      return () => clearTimeout(timer);
    }
  }, [isSaving, lastSaved, hasError]);

  const formatLastSaved = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) { // 小于1分钟
      return "刚刚保存";
    } else if (diff < 3600000) { // 小于1小时
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前保存`;
    } else {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    }
  };

  if (isSaving) {
    return (
      <div className="flex items-center gap-2 px-3 py-1.5 bg-blue-50 text-blue-700 rounded-md border border-blue-200">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm font-medium">保存中...</span>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex items-center gap-2 px-3 py-1.5 bg-red-50 text-red-700 rounded-md border border-red-200">
        <AlertCircle className="h-4 w-4" />
        <span className="text-sm font-medium">保存失败</span>
      </div>
    );
  }

  if (showSaved && lastSaved) {
    return (
      <div className={cn(
        "flex items-center gap-2 px-3 py-1.5 bg-green-50 text-green-700 rounded-md border border-green-200 transition-opacity duration-500",
        showSaved ? "opacity-100" : "opacity-0"
      )}>
        <Check className="h-4 w-4" />
        <span className="text-sm font-medium">已保存</span>
      </div>
    );
  }

  if (lastSaved) {
    return (
      <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-50 text-gray-600 rounded-md border border-gray-200">
        <Check className="h-4 w-4" />
        <span className="text-sm">{formatLastSaved(lastSaved)}</span>
      </div>
    );
  }

  return null;
};
