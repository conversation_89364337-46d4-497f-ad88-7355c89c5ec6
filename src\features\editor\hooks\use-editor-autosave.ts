/**
 * 编辑器自动保存Hook
 */
import { useEffect, useCallback, useRef } from 'react';
import { fabric } from 'fabric';
import { useAutoSaveDebounced } from '@/hooks/use-debounce';
import { projectService } from '@/lib/project-service';

interface UseEditorAutoSaveProps {
  canvas: fabric.Canvas | null;
  projectId: number | null;
  enabled?: boolean;
  delay?: number;
}

export function useEditorAutoSave({
  canvas,
  projectId,
  enabled = true,
  delay = 3000, // 3秒延迟
}: UseEditorAutoSaveProps) {
  const lastCanvasDataRef = useRef<string>('');

  // 获取画布数据的函数
  const getCanvasData = useCallback(() => {
    if (!canvas) return null;
    
    try {
      const canvasData = canvas.toJSON([
        'selectable',
        'evented',
        'id',
        'name',
        'locked',
        'visible',
        'opacity',
        'shadow',
        'filters',
        'clipPath',
        'globalCompositeOperation',
        'skewX',
        'skewY',
        'cropX',
        'cropY',
        'src',
        'crossOrigin',
        'filters',
      ]);
      
      return canvasData;
    } catch (error) {
      console.error('❌ Failed to serialize canvas:', error);
      return null;
    }
  }, [canvas]);

  // 保存函数
  const saveCanvasData = useCallback(async (canvasData: any) => {
    if (!projectId || !canvasData) {
      console.log('⏭️ Skipping save: no project ID or canvas data');
      return;
    }

    try {
      console.log('💾 Saving canvas data for project:', projectId);
      await projectService.saveProjectCanvas(projectId, canvasData);
      console.log('✅ Canvas data saved successfully');
    } catch (error) {
      console.error('❌ Failed to save canvas data:', error);
      throw error;
    }
  }, [projectId]);

  // 检查画布数据是否发生变化
  const hasCanvasChanged = useCallback((newData: any) => {
    if (!newData) return false;
    
    const newDataString = JSON.stringify(newData);
    const hasChanged = newDataString !== lastCanvasDataRef.current;
    
    if (hasChanged) {
      lastCanvasDataRef.current = newDataString;
    }
    
    return hasChanged;
  }, []);

  // 获取当前画布数据用于自动保存
  const getCurrentCanvasData = useCallback(() => {
    const canvasData = getCanvasData();
    
    // 只有当数据真正发生变化时才返回
    if (canvasData && hasCanvasChanged(canvasData)) {
      return canvasData;
    }
    
    return null;
  }, [getCanvasData, hasCanvasChanged]);

  // 使用自动保存Hook
  const { isSaving, lastSaved, error, saveNow } = useAutoSaveDebounced(
    saveCanvasData,
    getCurrentCanvasData(),
    delay,
    enabled && !!projectId && !!canvas
  );

  // 监听画布变化事件
  useEffect(() => {
    if (!canvas || !enabled) return;

    const handleCanvasChange = () => {
      // 触发重新获取画布数据
      const canvasData = getCanvasData();
      if (canvasData && hasCanvasChanged(canvasData)) {
        console.log('🎨 Canvas changed, will auto-save in', delay, 'ms');
      }
    };

    // 监听各种画布变化事件
    const events = [
      'object:added',
      'object:removed',
      'object:modified',
      'object:moving',
      'object:scaling',
      'object:rotating',
      'object:skewing',
      'path:created',
      'text:changed',
    ];

    events.forEach(event => {
      canvas.on(event, handleCanvasChange);
    });

    // 清理事件监听器
    return () => {
      events.forEach(event => {
        canvas.off(event, handleCanvasChange);
      });
    };
  }, [canvas, enabled, delay, getCanvasData, hasCanvasChanged]);

  // 手动保存函数
  const manualSave = useCallback(async () => {
    if (!canvas || !projectId) {
      console.log('⏭️ Cannot save: no canvas or project ID');
      return false;
    }

    try {
      const canvasData = getCanvasData();
      if (!canvasData) {
        console.log('⏭️ Cannot save: no canvas data');
        return false;
      }

      await saveCanvasData(canvasData);
      return true;
    } catch (error) {
      console.error('❌ Manual save failed:', error);
      return false;
    }
  }, [canvas, projectId, getCanvasData, saveCanvasData]);

  // 加载项目数据到画布
  const loadProjectData = useCallback(async (canvasData: any) => {
    if (!canvas || !canvasData) {
      console.log('⏭️ Cannot load: no canvas or data');
      return false;
    }

    try {
      console.log('📂 Loading project data to canvas');
      
      await new Promise<void>((resolve, reject) => {
        canvas.loadFromJSON(canvasData, () => {
          canvas.renderAll();
          
          // 更新最后保存的数据引用
          lastCanvasDataRef.current = JSON.stringify(canvasData);
          
          console.log('✅ Project data loaded successfully');
          resolve();
        }, (error: any) => {
          console.error('❌ Failed to load project data:', error);
          reject(error);
        });
      });
      
      return true;
    } catch (error) {
      console.error('❌ Load project data failed:', error);
      return false;
    }
  }, [canvas]);

  return {
    // 状态
    isSaving,
    lastSaved,
    error,
    
    // 方法
    saveNow,
    manualSave,
    loadProjectData,
    getCanvasData,
  };
}
