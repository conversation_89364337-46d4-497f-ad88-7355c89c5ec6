import { fabric } from "fabric";
import { useCallback, useState, useMemo, useRef } from "react";

import { 
  Editor, 
  FILL_COLOR,
  STROKE_WIDTH,
  STROKE_COLOR,
  CIRCLE_OPTIONS,
  DIAMOND_OPTIONS,
  TRIANGLE_OPTIONS,
  BuildEditorProps, 
  RECTANGLE_OPTIONS,
  EditorHookProps,
  STROKE_DASH_ARRAY,
  TEXT_OPTIONS,
  FONT_FAMILY,
  FONT_WEIGHT,
  FONT_SIZE,
  JSON_KEYS,
} from "@/features/editor/types";
import { useHistory } from "@/features/editor/hooks/use-history";
import {
  createFilter,
  downloadFile,
  isTextType,
  transformText
} from "@/features/editor/utils";
import { RoundedRect } from "@/features/editor/utils/rounded-rect";
import { useHotkeys } from "@/features/editor/hooks/use-hotkeys";
import { useClipboard } from "@/features/editor/hooks//use-clipboard";
import { useAutoResize } from "@/features/editor/hooks/use-auto-resize";
import { useCanvasEvents } from "@/features/editor/hooks/use-canvas-events";
import { useWindowEvents } from "@/features/editor/hooks/use-window-events";
import { useLoadState } from "@/features/editor/hooks/use-load-state";

const buildEditor = ({
  save,
  undo,
  redo,
  canRedo,
  canUndo,
  autoZoom,
  copy,
  paste,
  canvas,
  fillColor,
  fontFamily,
  setFontFamily,
  setFillColor,
  strokeColor,
  setStrokeColor,
  strokeWidth,
  setStrokeWidth,
  selectedObjects,
  strokeDashArray,
  setStrokeDashArray,
}: BuildEditorProps): Editor => {
  const generateSaveOptions = () => {
    const { width, height, left, top } = getWorkspace() as fabric.Rect;

    return {
      name: "Image",
      format: "png",
      quality: 1,
      width,
      height,
      left,
      top,
    };
  };

  const savePng = () => {
    const options = generateSaveOptions();

    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    const dataUrl = canvas.toDataURL(options);

    downloadFile(dataUrl, "png");
    autoZoom();
  };

  const saveSvg = () => {
    const options = generateSaveOptions();

    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    const dataUrl = canvas.toDataURL(options);

    downloadFile(dataUrl, "svg");
    autoZoom();
  };

  const saveJpg = () => {
    const options = generateSaveOptions();

    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    const dataUrl = canvas.toDataURL(options);

    downloadFile(dataUrl, "jpg");
    autoZoom();
  };

  const saveJson = async () => {
    const dataUrl = canvas.toJSON(JSON_KEYS);

    await transformText(dataUrl.objects);
    const fileString = `data:text/json;charset=utf-8,${encodeURIComponent(
      JSON.stringify(dataUrl, null, "\t"),
    )}`;
    downloadFile(fileString, "json");
  };

  const loadJson = (json: string) => {
    const data = JSON.parse(json);

    canvas.loadFromJSON(data, () => {
      autoZoom();
    });
  };

  const getWorkspace = () => {
    return canvas
    .getObjects()
    .find((object) => object.name === "clip");
  };

  const center = (object: fabric.Object) => {
    const workspace = getWorkspace();

    if (!workspace) return;

    const workspaceCenter = workspace.getCenterPoint();

    if (!workspaceCenter) return;

    // 手动计算并设置对象位置到工作区中心
    object.set({
      left: workspaceCenter.x,
      top: workspaceCenter.y,
      originX: 'center',
      originY: 'center'
    });

    object.setCoords();
  };

  const addToCanvas = (object: fabric.Object) => {
    // Generate unique ID and name for the object
    const objectType = object.type === "i-text" || object.type === "textbox" ? "Text" :
                      object.type === "image" ? "Image" :
                      object.type === "circle" ? "Circle" :
                      object.type === "rect" ? "Rectangle" :
                      object.type === "triangle" ? "Triangle" : "Shape";

    const existingObjects = canvas.getObjects().filter(obj => obj.name !== "clip");
    const sameTypeCount = existingObjects.filter(obj => {
      const objType = obj.type === "i-text" || obj.type === "textbox" ? "Text" :
                     obj.type === "image" ? "Image" :
                     obj.type === "circle" ? "Circle" :
                     obj.type === "rect" ? "Rectangle" :
                     obj.type === "triangle" ? "Triangle" : "Shape";
      return objType === objectType;
    }).length;

    (object as any).id = `${objectType.toLowerCase()}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    (object as any).name = `${objectType} ${sameTypeCount + 1}`;

    // 先添加到画布，然后居中
    canvas.add(object);
    center(object);
    canvas.setActiveObject(object);
    canvas.renderAll();
  };

  return {
    savePng,
    saveJpg,
    saveSvg,
    saveJson,
    loadJson,
    canUndo,
    canRedo,
    autoZoom,
    getWorkspace,
    zoomIn: () => {
      let zoomRatio = canvas.getZoom();
      zoomRatio += 0.05;
      const center = canvas.getCenter();
      canvas.zoomToPoint(
        new fabric.Point(center.left, center.top),
        zoomRatio > 1 ? 1 : zoomRatio
      );
    },
    zoomOut: () => {
      let zoomRatio = canvas.getZoom();
      zoomRatio -= 0.05;
      const center = canvas.getCenter();
      canvas.zoomToPoint(
        new fabric.Point(center.left, center.top),
        zoomRatio < 0.2 ? 0.2 : zoomRatio,
      );
    },
    changeSize: (value: { width: number; height: number }) => {
      const workspace = getWorkspace();

      if (!workspace) return;

      // 更新工作区尺寸
      workspace.set(value);

      // 不使用clipPath，避免裁剪滤镜效果
      canvas.clipPath = null;

      // 调用autoZoom来重新调整视图
      autoZoom();

      // 强制重新渲染
      canvas.requestRenderAll();

      // 保存更改
      save();
    },
    changeBackground: (value: string) => {
      const workspace = getWorkspace();
      workspace?.set({ fill: value });
      canvas.renderAll();
      save();
    },
    enableDrawingMode: () => {
      canvas.discardActiveObject();
      canvas.renderAll();
      canvas.isDrawingMode = true;
      canvas.freeDrawingBrush.width = strokeWidth;
      canvas.freeDrawingBrush.color = strokeColor;
    },
    disableDrawingMode: () => {
      canvas.isDrawingMode = false;
    },
    onUndo: () => undo(),
    onRedo: () => redo(),
    onCopy: () => copy(),
    onPaste: () => paste(),
    changeImageFilter: (value: string) => {
      const objects = canvas.getActiveObjects();
      objects.forEach((object) => {
        if (object.type === "image") {
          const imageObject = object as fabric.Image;

          const effect = createFilter(value);

          imageObject.filters = effect ? [effect] : [];
          imageObject.applyFilters();
          canvas.renderAll();
        }
      });
    },
    addImage: (value: string) => {
      fabric.Image.fromURL(
        value,
        (image) => {
          const workspace = getWorkspace();

          image.scaleToWidth(workspace?.width || 0);
          image.scaleToHeight(workspace?.height || 0);

          addToCanvas(image);
        },
        {
          crossOrigin: "anonymous",
        },
      );
    },
    delete: () => {
      canvas.getActiveObjects().forEach((object) => canvas.remove(object));
      canvas.discardActiveObject();
      canvas.renderAll();
    },
    addText: (value, options) => {
      const object = new fabric.Textbox(value, {
        ...TEXT_OPTIONS,
        fill: fillColor,
        ...options,
      });

      addToCanvas(object);
    },
    getActiveOpacity: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return 1;
      }

      const value = selectedObject.get("opacity") || 1;

      return value;
    },
    changeFontSize: (value: number) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontSize exists.
          object.set({ fontSize: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontSize: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return FONT_SIZE;
      }

      // @ts-ignore
      // Faulty TS library, fontSize exists.
      const value = selectedObject.get("fontSize") || FONT_SIZE;

      return value;
    },
    changeTextAlign: (value: string) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, textAlign exists.
          object.set({ textAlign: value });
        }
      });
      canvas.renderAll();
    },
    getActiveTextAlign: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return "left";
      }

      // @ts-ignore
      // Faulty TS library, textAlign exists.
      const value = selectedObject.get("textAlign") || "left";

      return value;
    },
    changeFontUnderline: (value: boolean) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, underline exists.
          object.set({ underline: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontUnderline: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return false;
      }

      // @ts-ignore
      // Faulty TS library, underline exists.
      const value = selectedObject.get("underline") || false;

      return value;
    },
    changeFontLinethrough: (value: boolean) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, linethrough exists.
          object.set({ linethrough: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontLinethrough: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return false;
      }

      // @ts-ignore
      // Faulty TS library, linethrough exists.
      const value = selectedObject.get("linethrough") || false;

      return value;
    },
    changeFontStyle: (value: string) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontStyle exists.
          object.set({ fontStyle: value });
        }
      });
      canvas.renderAll();
    },
    getActiveFontStyle: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return "normal";
      }

      // @ts-ignore
      // Faulty TS library, fontStyle exists.
      const value = selectedObject.get("fontStyle") || "normal";

      return value;
    },
    changeFontWeight: (value: number) => {
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontWeight exists.
          object.set({ fontWeight: value });
        }
      });
      canvas.renderAll();
    },
    changeOpacity: (value: number) => {
      canvas.getActiveObjects().forEach((object) => {
        object.set({ opacity: value });
      });
      canvas.renderAll();
    },
    bringForward: () => {
      canvas.getActiveObjects().forEach((object) => {
        canvas.bringForward(object);
      });

      canvas.renderAll();
      
      const workspace = getWorkspace();
      workspace?.sendToBack();
    },
    sendBackwards: () => {
      canvas.getActiveObjects().forEach((object) => {
        canvas.sendBackwards(object);
      });

      canvas.renderAll();
      const workspace = getWorkspace();
      workspace?.sendToBack();
    },
    changeFontFamily: (value: string) => {
      setFontFamily(value);
      canvas.getActiveObjects().forEach((object) => {
        if (isTextType(object.type)) {
          // @ts-ignore
          // Faulty TS library, fontFamily exists.
          object.set({ fontFamily: value });
        }
      });
      canvas.renderAll();
    },
    changeFillColor: (value: string) => {
      setFillColor(value);
      canvas.getActiveObjects().forEach((object) => {
        object.set({ fill: value });
      });
      canvas.renderAll();
    },
    changeStrokeColor: (value: string) => {
      setStrokeColor(value);
      canvas.getActiveObjects().forEach((object) => {
        // Text types don't have stroke
        if (isTextType(object.type)) {
          object.set({ fill: value });
          return;
        }

        object.set({ stroke: value });
      });
      canvas.freeDrawingBrush.color = value;
      canvas.renderAll();
    },
    changeStrokeWidth: (value: number) => {
      setStrokeWidth(value);
      canvas.getActiveObjects().forEach((object) => {
        object.set({ strokeWidth: value });
      });
      canvas.freeDrawingBrush.width = value;
      canvas.renderAll();
    },
    changeStrokeDashArray: (value: number[]) => {
      setStrokeDashArray(value);
      canvas.getActiveObjects().forEach((object) => {
        object.set({ strokeDashArray: value });
      });
      canvas.renderAll();
    },
    addCircle: () => {
      const object = new fabric.Circle({
        ...CIRCLE_OPTIONS,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addSoftRectangle: () => {
      const object = new fabric.Rect({
        ...RECTANGLE_OPTIONS,
        rx: 50,
        ry: 50,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addRectangle: () => {
      const object = new RoundedRect({
        ...RECTANGLE_OPTIONS,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addTriangle: () => {
      const object = new fabric.Triangle({
        ...TRIANGLE_OPTIONS,
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        strokeDashArray: strokeDashArray,
      });

      addToCanvas(object);
    },
    addInverseTriangle: () => {
      const HEIGHT = TRIANGLE_OPTIONS.height;
      const WIDTH = TRIANGLE_OPTIONS.width;

      const object = new fabric.Polygon(
        [
          { x: 0, y: 0 },
          { x: WIDTH, y: 0 },
          { x: WIDTH / 2, y: HEIGHT },
        ],
        {
          ...TRIANGLE_OPTIONS,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          strokeDashArray: strokeDashArray,
        }
      );

      addToCanvas(object);
    },
    addDiamond: () => {
      const HEIGHT = DIAMOND_OPTIONS.height;
      const WIDTH = DIAMOND_OPTIONS.width;

      const object = new fabric.Polygon(
        [
          { x: WIDTH / 2, y: 0 },
          { x: WIDTH, y: HEIGHT / 2 },
          { x: WIDTH / 2, y: HEIGHT },
          { x: 0, y: HEIGHT / 2 },
        ],
        {
          ...DIAMOND_OPTIONS,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          strokeDashArray: strokeDashArray,
        }
      );
      addToCanvas(object);
    },
    changeBorderRadius: (value: number) => {
      canvas.getActiveObjects().forEach((object) => {
        if (object.type === 'rect') {
          (object as any).set({
            rx: value,
            ry: value,
          });
        } else if (object.type === 'group') {
          // Apply to all rectangles in the group
          (object as any).getObjects().forEach((obj: any) => {
            if (obj.type === 'rect') {
              obj.set({
                rx: value,
                ry: value,
              });
            }
          });
        }
      });
      canvas.renderAll();
    },
    getActiveBorderRadius: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return 0;
      }

      if (selectedObject.type === 'rect') {
        return (selectedObject as any).rx || 0;
      }

      return 0;
    },
    canvas,
    getActiveFontWeight: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return FONT_WEIGHT;
      }

      // @ts-ignore
      // Faulty TS library, fontWeight exists.
      const value = selectedObject.get("fontWeight") || FONT_WEIGHT;

      return value;
    },
    getActiveFontFamily: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return fontFamily;
      }

      // @ts-ignore
      // Faulty TS library, fontFamily exists.
      const value = selectedObject.get("fontFamily") || fontFamily;

      return value;
    },
    getActiveFillColor: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return fillColor;
      }

      const value = selectedObject.get("fill") || fillColor;

      // Currently, gradients & patterns are not supported
      return value as string;
    },
    getActiveStrokeColor: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return strokeColor;
      }

      const value = selectedObject.get("stroke") || strokeColor;

      return value;
    },
    getActiveStrokeWidth: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return strokeWidth;
      }

      const value = selectedObject.get("strokeWidth") || strokeWidth;

      return value;
    },
    getActiveStrokeDashArray: () => {
      const selectedObject = selectedObjects[0];

      if (!selectedObject) {
        return strokeDashArray;
      }

      const value = selectedObject.get("strokeDashArray") || strokeDashArray;

      return value;
    },
    selectedObjects,
    // New image editing methods
    startCrop: (ratio: number | null) => {
      const activeObject = canvas.getActiveObject();
      if (activeObject && activeObject.type === "image") {
        const imageObject = activeObject as fabric.Image;

        // Store original dimensions for restoration
        (imageObject as any).originalWidth = imageObject.width;
        (imageObject as any).originalHeight = imageObject.height;
        (imageObject as any).originalScaleX = imageObject.scaleX;
        (imageObject as any).originalScaleY = imageObject.scaleY;

        // Create crop rectangle overlay
        const cropRect = new fabric.Rect({
          left: imageObject.left || 0,
          top: imageObject.top || 0,
          width: (imageObject.width || 0) * (imageObject.scaleX || 1),
          height: (imageObject.height || 0) * (imageObject.scaleY || 1),
          fill: 'transparent',
          stroke: '#007bff',
          strokeWidth: 2,
          strokeDashArray: [5, 5],
          selectable: true,
          hasControls: true,
          hasBorders: true,
          name: 'cropRect'
        });

        // Apply aspect ratio if specified
        if (ratio) {
          const currentWidth = cropRect.width || 0;
          const newHeight = currentWidth / ratio;
          cropRect.set({ height: newHeight });
        }

        // Store reference to the image being cropped
        (cropRect as any).targetImage = imageObject;

        // Add crop rectangle to canvas
        canvas.add(cropRect);
        canvas.setActiveObject(cropRect);
        canvas.renderAll();

        // Store crop state
        (canvas as any).cropMode = true;
        (canvas as any).cropRect = cropRect;
      }
    },
    applyCrop: () => {
      const cropRect = (canvas as any).cropRect;
      if (cropRect && (canvas as any).cropMode) {
        const imageObject = (cropRect as any).targetImage;

        if (imageObject) {
          // Calculate crop parameters
          const cropLeft = (cropRect.left || 0) - (imageObject.left || 0);
          const cropTop = (cropRect.top || 0) - (imageObject.top || 0);
          const cropWidth = cropRect.width || 0;
          const cropHeight = cropRect.height || 0;

          // Apply crop by creating a new image element
          const imgElement = imageObject.getElement();
          const tempCanvas = document.createElement('canvas');
          const tempCtx = tempCanvas.getContext('2d');

          if (tempCtx && imgElement) {
            tempCanvas.width = cropWidth;
            tempCanvas.height = cropHeight;

            // Draw cropped portion
            tempCtx.drawImage(
              imgElement,
              cropLeft / (imageObject.scaleX || 1),
              cropTop / (imageObject.scaleY || 1),
              cropWidth / (imageObject.scaleX || 1),
              cropHeight / (imageObject.scaleY || 1),
              0,
              0,
              cropWidth,
              cropHeight
            );

            // Create new fabric image from cropped canvas
            fabric.Image.fromURL(tempCanvas.toDataURL(), (newImg) => {
              newImg.set({
                left: cropRect.left,
                top: cropRect.top,
                scaleX: 1,
                scaleY: 1
              });

              // Replace original image with cropped version
              canvas.remove(imageObject);
              canvas.add(newImg);
              canvas.setActiveObject(newImg);
              canvas.renderAll();
              save();
            });
          }
        }

        // Clean up crop mode
        canvas.remove(cropRect);
        (canvas as any).cropMode = false;
        (canvas as any).cropRect = null;
        canvas.renderAll();
      }
    },
    cancelCrop: () => {
      const cropRect = (canvas as any).cropRect;
      if (cropRect && (canvas as any).cropMode) {
        // Remove crop rectangle
        canvas.remove(cropRect);

        // Clean up crop mode
        (canvas as any).cropMode = false;
        (canvas as any).cropRect = null;
        canvas.renderAll();
      }
    },
    changeImageBlur: (value: number) => {
      const objects = canvas.getActiveObjects();
      objects.forEach((object) => {
        if (object.type === "image") {
          const imageObject = object as fabric.Image;

          // Initialize filters array if it doesn't exist
          if (!imageObject.filters) {
            imageObject.filters = [];
          }

          // Remove existing blur filters
          imageObject.filters = imageObject.filters.filter(
            filter => !(filter instanceof fabric.Image.filters.Blur)
          );

          // Add new blur filter if value > 0
          if (value > 0) {
            // Fabric.js Blur滤镜的blur值应该是0-1之间
            // 根据Fabric.js文档，blur值越大效果越明显
            const blurFilter = new fabric.Image.filters.Blur({
              blur: Math.min(value / 50, 1) // 将0-50px转换为0-1的范围，最大值为1
            });
            imageObject.filters.push(blurFilter);
          }

          // 临时移除clipPath以确保滤镜效果不被裁剪
          const originalClipPath = imageObject.clipPath;
          if (value > 0) {
            imageObject.clipPath = null;
          } else if (originalClipPath) {
            // 如果没有模糊效果，恢复原来的clipPath
            imageObject.clipPath = originalClipPath;
          }

          // Apply filters and render
          imageObject.applyFilters();
          canvas.renderAll();
        }
      });

      // 移除全局clipPath以避免裁剪滤镜效果
      if (value > 0) {
        canvas.clipPath = null;
      }
      canvas.requestRenderAll();

      // Only save if blur is applied (not during real-time preview)
      if (value === 0 || value % 5 === 0) {
        save();
      }
    },
    applyImageMask: (maskType: string) => {
      const activeObject = canvas.getActiveObject();
      if (activeObject && activeObject.type === "image") {
        const imageObject = activeObject as fabric.Image;

        // Get actual dimensions including scale
        const scaledWidth = (imageObject.width || 0) * (imageObject.scaleX || 1);
        const scaledHeight = (imageObject.height || 0) * (imageObject.scaleY || 1);
        const centerX = scaledWidth / 2;
        const centerY = scaledHeight / 2;

        // Create clip path based on mask type
        let clipPath;

        switch (maskType) {
          case "circle":
            clipPath = new fabric.Circle({
              radius: Math.min(scaledWidth, scaledHeight) / 2,
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
            break;
          case "square":
            const squareSize = Math.min(scaledWidth, scaledHeight);
            clipPath = new fabric.Rect({
              width: squareSize,
              height: squareSize,
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
            break;
          case "triangle":
            clipPath = new fabric.Triangle({
              width: scaledWidth * 0.8,
              height: scaledHeight * 0.8,
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
            break;
          case "heart":
            // Create heart shape using path
            const heartPath = "M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5 C2,5.42,4.42,3,7.5,3c1.74,0,3.41,0.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3 C19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54L12,21.35z";
            clipPath = new fabric.Path(heartPath, {
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
              scaleX: scaledWidth / 100,
              scaleY: scaledHeight / 100,
            });
            break;
          case "star":
            // Create star shape
            const starPoints = [];
            const outerRadius = Math.min(scaledWidth, scaledHeight) / 2;
            const innerRadius = outerRadius * 0.4;
            for (let i = 0; i < 10; i++) {
              const angle = (i * Math.PI) / 5;
              const radius = i % 2 === 0 ? outerRadius : innerRadius;
              starPoints.push({
                x: centerX + radius * Math.cos(angle - Math.PI / 2),
                y: centerY + radius * Math.sin(angle - Math.PI / 2)
              });
            }
            const starPath = starPoints.map((point, index) =>
              `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
            ).join(' ') + ' Z';

            clipPath = new fabric.Path(starPath, {
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
            break;
          case "hexagon":
            // Create hexagon shape
            const hexPoints = [];
            const hexRadius = Math.min(scaledWidth, scaledHeight) / 2;
            for (let i = 0; i < 6; i++) {
              const angle = (i * Math.PI) / 3;
              hexPoints.push({
                x: centerX + hexRadius * Math.cos(angle),
                y: centerY + hexRadius * Math.sin(angle)
              });
            }
            const hexPath = hexPoints.map((point, index) =>
              `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
            ).join(' ') + ' Z';

            clipPath = new fabric.Path(hexPath, {
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
            break;
          case "rounded-square":
            clipPath = new fabric.Rect({
              width: Math.min(scaledWidth, scaledHeight),
              height: Math.min(scaledWidth, scaledHeight),
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
              rx: 20,
              ry: 20,
            });
            break;
          case "oval":
            clipPath = new fabric.Ellipse({
              rx: scaledWidth / 2,
              ry: scaledHeight / 2,
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
            break;
          case "diamond":
            const diamondPath = `M ${centerX} ${centerY - scaledHeight/2} L ${centerX + scaledWidth/2} ${centerY} L ${centerX} ${centerY + scaledHeight/2} L ${centerX - scaledWidth/2} ${centerY} Z`;
            clipPath = new fabric.Path(diamondPath, {
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
            break;
          default:
            clipPath = new fabric.Circle({
              radius: Math.min(scaledWidth, scaledHeight) / 2,
              left: centerX,
              top: centerY,
              originX: "center",
              originY: "center",
            });
        }

        // Apply the clip path
        imageObject.clipPath = clipPath;
        canvas.renderAll();
        save();
      }
    },
    removeImageMask: () => {
      const activeObject = canvas.getActiveObject();
      if (activeObject && activeObject.type === "image") {
        const imageObject = activeObject as fabric.Image;
        imageObject.clipPath = undefined;
        canvas.renderAll();
        save();
      }
    },
    // Layer management methods
    toggleLayerVisibility: (layerId: string) => {
      const objects = canvas.getObjects();
      const targetObject = objects.find(obj => {
        // Try to find by id first, then by index
        return (obj as any).id === layerId ||
               objects.indexOf(obj).toString() === layerId.replace('layer_', '');
      });

      if (targetObject) {
        targetObject.set('visible', !targetObject.visible);
        canvas.renderAll();
        save();
      }
    },
    toggleLayerLock: (layerId: string) => {
      const objects = canvas.getObjects();
      const targetObject = objects.find(obj => {
        return (obj as any).id === layerId ||
               objects.indexOf(obj).toString() === layerId.replace('layer_', '');
      });

      if (targetObject) {
        const isLocked = !targetObject.selectable;
        targetObject.set({
          selectable: isLocked,
          evented: isLocked,
          hoverCursor: isLocked ? 'default' : 'move',
          moveCursor: isLocked ? 'default' : 'move'
        });

        // If currently selected and being locked, deselect
        if (!isLocked && canvas.getActiveObject() === targetObject) {
          canvas.discardActiveObject();
        }

        canvas.renderAll();
      }
    },
    selectLayer: (layerId: string) => {
      const objects = canvas.getObjects();
      const targetObject = objects.find(obj => {
        return (obj as any).id === layerId ||
               objects.indexOf(obj).toString() === layerId.replace('layer_', '');
      });

      if (targetObject && targetObject.selectable) {
        canvas.setActiveObject(targetObject);
        canvas.renderAll();
      }
    },
    deleteLayer: (layerId: string) => {
      const objects = canvas.getObjects();
      const targetObject = objects.find(obj => {
        return (obj as any).id === layerId ||
               objects.indexOf(obj).toString() === layerId.replace('layer_', '');
      });

      if (targetObject && targetObject.name !== "clip") { // Don't delete workspace
        canvas.remove(targetObject);
        canvas.renderAll();
        save();
      }
    },
  };
};

export const useEditor = ({
  defaultState,
  defaultHeight,
  defaultWidth,
  clearSelectionCallback,
  saveCallback,
}: EditorHookProps) => {
  const initialState = useRef(defaultState);
  const initialWidth = useRef(defaultWidth);
  const initialHeight = useRef(defaultHeight);

  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);
  const [container, setContainer] = useState<HTMLDivElement | null>(null);
  const [selectedObjects, setSelectedObjects] = useState<fabric.Object[]>([]);

  const [fontFamily, setFontFamily] = useState(FONT_FAMILY);
  const [fillColor, setFillColor] = useState(FILL_COLOR);
  const [strokeColor, setStrokeColor] = useState(STROKE_COLOR);
  const [strokeWidth, setStrokeWidth] = useState(STROKE_WIDTH);
  const [strokeDashArray, setStrokeDashArray] = useState<number[]>(STROKE_DASH_ARRAY);

  useWindowEvents();

  const { 
    save, 
    canRedo, 
    canUndo, 
    undo, 
    redo,
    canvasHistory,
    setHistoryIndex,
  } = useHistory({ 
    canvas,
    saveCallback
  });

  const { copy, paste } = useClipboard({ canvas });

  const { autoZoom } = useAutoResize({
    canvas,
    container,
  });

  useCanvasEvents({
    save,
    canvas,
    setSelectedObjects,
    clearSelectionCallback,
  });

  useHotkeys({
    undo,
    redo,
    copy,
    paste,
    save,
    canvas,
  });

  useLoadState({
    canvas,
    autoZoom,
    initialState,
    canvasHistory,
    setHistoryIndex,
  });

  const editor = useMemo(() => {
    if (canvas) {
      return buildEditor({
        save,
        undo,
        redo,
        canUndo,
        canRedo,
        autoZoom,
        copy,
        paste,
        canvas,
        fillColor,
        strokeWidth,
        strokeColor,
        setFillColor,
        setStrokeColor,
        setStrokeWidth,
        strokeDashArray,
        selectedObjects,
        setStrokeDashArray,
        fontFamily,
        setFontFamily,
      });
    }

    return undefined;
  }, 
  [
    canRedo,
    canUndo,
    undo,
    redo,
    save,
    autoZoom,
    copy,
    paste,
    canvas,
    fillColor,
    strokeWidth,
    strokeColor,
    selectedObjects,
    strokeDashArray,
    fontFamily,
  ]);

  const init = useCallback(
    ({
      initialCanvas,
      initialContainer,
    }: {
      initialCanvas: fabric.Canvas;
      initialContainer: HTMLDivElement;
    }) => {
      fabric.Object.prototype.set({
        cornerColor: "#FFF",
        cornerStyle: "circle",
        borderColor: "#3b82f6",
        borderScaleFactor: 1.5,
        transparentCorners: false,
        borderOpacityWhenMoving: 1,
        cornerStrokeColor: "#3b82f6",
      });

      const initialWorkspace = new fabric.Rect({
        width: initialWidth.current,
        height: initialHeight.current,
        name: "clip",
        fill: "white",
        selectable: false,
        hasControls: false,
        left: initialContainer.offsetWidth / 2,
        top: initialContainer.offsetHeight / 2,
        originX: 'center',
        originY: 'center',
        shadow: new fabric.Shadow({
          color: "rgba(0,0,0,0.8)",
          blur: 5,
        }),
      });

      initialCanvas.setWidth(initialContainer.offsetWidth);
      initialCanvas.setHeight(initialContainer.offsetHeight);

      initialCanvas.add(initialWorkspace);
      // 不设置clipPath，避免裁剪滤镜效果
      // initialCanvas.clipPath = initialWorkspace;

      setCanvas(initialCanvas);
      setContainer(initialContainer);

      const currentState = JSON.stringify(
        initialCanvas.toJSON(JSON_KEYS)
      );
      canvasHistory.current = [currentState];
      setHistoryIndex(0);

      // 确保画布正确居中
      setTimeout(() => {
        // 使用当前的canvas和container状态
        if (canvas && container) {
          autoZoom();
        }
      }, 200);
    },
    [] // 移除依赖项，因为canvasHistory是useRef，setHistoryIndex是useState setter
  );

  return { init, editor };
};
