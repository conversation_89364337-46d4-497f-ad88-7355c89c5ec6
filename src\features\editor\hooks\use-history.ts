import { fabric } from "fabric";
import { useCallback, useRef, useState } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseHistoryProps {
  canvas: fabric.Canvas | null;
  saveCallback?: (values: {
    canvas_data: any;
    height: number;
    width: number;
    thumbnail_url?: string | null;
  }) => void;
};

export const useHistory = ({ canvas, saveCallback }: UseHistoryProps) => {
  const [historyIndex, setHistoryIndex] = useState(0);
  const canvasHistory = useRef<string[]>([]);
  const skipSave = useRef(false);

  const canUndo = useCallback(() => {
    return historyIndex > 0;
  }, [historyIndex]);

  const canRedo = useCallback(() => {
    return historyIndex < canvasHistory.current.length - 1;
  }, [historyIndex]);

  const save = useCallback((skip = false) => {
    if (!canvas) return;

    const currentState = canvas.toJSON(JSON_KEYS);
    const json = JSON.stringify(currentState);

    if (!skip && !skipSave.current) {
      // 如果当前不是在历史记录的最后位置，删除后面的历史记录
      if (historyIndex < canvasHistory.current.length - 1) {
        canvasHistory.current = canvasHistory.current.slice(0, historyIndex + 1);
      }

      // 检查是否与最后一个状态相同，避免重复保存
      const lastState = canvasHistory.current[canvasHistory.current.length - 1];
      if (lastState !== json) {
        canvasHistory.current.push(json);
        setHistoryIndex(canvasHistory.current.length - 1);

        // 限制历史记录数量，避免内存过大
        if (canvasHistory.current.length > 50) {
          canvasHistory.current = canvasHistory.current.slice(-50);
          setHistoryIndex(49);
        }
      }
    }

    const workspace = canvas
      .getObjects()
      .find((object) => object.name === "clip");
    const height = workspace?.height || 0;
    const width = workspace?.width || 0;

    // 生成缩略图
    const generateThumbnail = () => {
      try {
        // 使用工作区尺寸
        console.log('📸 Generating thumbnail for workspace:', width, 'x', height);

        if (width <= 0 || height <= 0) {
          console.error('❌ Invalid workspace size for thumbnail generation');
          return null;
        }

        // 保存当前视口变换
        const currentTransform = canvas.viewportTransform;

        // 重置视口变换以获得正确的缩略图
        canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

        // 找到工作区对象
        const workspace = canvas.getObjects().find((object) => object.name === "clip");
        if (!workspace) {
          console.error('❌ Workspace not found for thumbnail generation');
          return null;
        }

        // 计算包含所有对象（包括滤镜效果）的边界框
        const objects = canvas.getObjects().filter(obj => obj.name !== "clip");
        let minX = workspace.left! - width / 2;
        let minY = workspace.top! - height / 2;
        let maxX = workspace.left! + width / 2;
        let maxY = workspace.top! + height / 2;

        // 检查是否有模糊效果，如果有则扩展边界
        const hasBlurEffects = objects.some(obj => {
          if (obj.type === 'image' && (obj as fabric.Image).filters) {
            return (obj as fabric.Image).filters!.some(filter =>
              filter instanceof fabric.Image.filters.Blur
            );
          }
          return false;
        });

        if (hasBlurEffects) {
          const padding = 50; // 为模糊效果预留空间
          minX -= padding;
          minY -= padding;
          maxX += padding;
          maxY += padding;
        }

        const captureWidth = maxX - minX;
        const captureHeight = maxY - minY;

        // 生成高质量缩略图
        const maxThumbnailSize = 400;
        const aspectRatio = captureWidth / captureHeight;

        let thumbnailWidth, thumbnailHeight;
        if (aspectRatio > 1) {
          thumbnailWidth = Math.min(maxThumbnailSize, captureWidth);
          thumbnailHeight = thumbnailWidth / aspectRatio;
        } else {
          thumbnailHeight = Math.min(maxThumbnailSize, captureHeight);
          thumbnailWidth = thumbnailHeight * aspectRatio;
        }

        const multiplier = Math.min(
          thumbnailWidth / captureWidth,
          thumbnailHeight / captureHeight,
          2
        );

        console.log('📸 Thumbnail multiplier:', multiplier);
        console.log('📸 Target size:', `${thumbnailWidth}x${thumbnailHeight}`);
        console.log('📸 Capture area:', `${captureWidth}x${captureHeight} at (${minX}, ${minY})`);

        // 截取包含滤镜效果的区域
        const thumbnailDataUrl = canvas.toDataURL({
          format: 'png',
          quality: 0.9,
          multiplier: multiplier,
          left: minX,
          top: minY,
          width: captureWidth,
          height: captureHeight
        });

        // 恢复视口变换
        if (currentTransform) {
          canvas.setViewportTransform(currentTransform);
        }

        console.log('✅ Thumbnail generated successfully, length:', thumbnailDataUrl.length);

        // 检查缩略图大小，如果太大则压缩
        if (thumbnailDataUrl.length > 500000) { // 500KB限制
          console.log('⚠️ Thumbnail too large, compressing...');

          const compressedThumbnail = canvas.toDataURL({
            format: 'jpeg',
            quality: 0.7, // 提高压缩质量
            multiplier: multiplier * 0.8 // 稍微减小尺寸
          });

          console.log('✅ Compressed thumbnail length:', compressedThumbnail.length);
          return compressedThumbnail;
        }

        return thumbnailDataUrl;
      } catch (error) {
        console.error('❌ Failed to generate thumbnail:', error);
        return null;
      }
    };

    const thumbnail = generateThumbnail();
    console.log('📸 Thumbnail result:', thumbnail ? 'Generated' : 'Failed');

    saveCallback?.({
      canvas_data: JSON.parse(json),
      height,
      width,
      thumbnail_url: thumbnail
    });
  },
  [
    canvas,
    saveCallback,
    historyIndex,
  ]);

  const undo = useCallback(() => {
    if (canUndo()) {
      skipSave.current = true;

      const previousIndex = historyIndex - 1;
      const previousState = JSON.parse(
        canvasHistory.current[previousIndex]
      );

      // 清除画布并加载之前的状态
      canvas?.clear();
      canvas?.loadFromJSON(previousState, () => {
        // 确保工作区在最底层
        const workspace = canvas.getObjects().find(obj => obj.name === "clip");
        if (workspace) {
          workspace.sendToBack();
        }

        canvas.renderAll();
        setHistoryIndex(previousIndex);
        skipSave.current = false;

        // 触发图层更新事件，确保图层面板同步
        setTimeout(() => {
          // 触发多个事件确保图层面板正确更新
          canvas.fire('object:added', {});
          canvas.fire('path:created', {});
          canvas.fire('object:modified', {});
        }, 150);
      });
    }
  }, [canUndo, canvas, historyIndex]);

  const redo = useCallback(() => {
    if (canRedo()) {
      skipSave.current = true;

      const nextIndex = historyIndex + 1;
      const nextState = JSON.parse(
        canvasHistory.current[nextIndex]
      );

      // 清除画布并加载下一个状态
      canvas?.clear();
      canvas?.loadFromJSON(nextState, () => {
        // 确保工作区在最底层
        const workspace = canvas.getObjects().find(obj => obj.name === "clip");
        if (workspace) {
          workspace.sendToBack();
        }

        canvas.renderAll();
        setHistoryIndex(nextIndex);
        skipSave.current = false;

        // 触发图层更新事件，确保图层面板同步
        setTimeout(() => {
          // 触发多个事件确保图层面板正确更新
          canvas.fire('object:added', {});
          canvas.fire('path:created', {});
          canvas.fire('object:modified', {});
        }, 150);
      });
    }
  }, [canvas, historyIndex, canRedo]);

  return { 
    save,
    canUndo,
    canRedo,
    undo,
    redo,
    setHistoryIndex,
    canvasHistory,
  };
};
