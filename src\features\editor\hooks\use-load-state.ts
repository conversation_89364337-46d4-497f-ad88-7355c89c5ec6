import { fabric } from "fabric";
import { useEffect, useRef } from "react";

import { JSON_KEYS } from "@/features/editor/types";

interface UseLoadStateProps {
  autoZoom: () => void;
  canvas: fabric.Canvas | null;
  initialState: React.MutableRefObject<string | any | undefined>;
  canvasHistory: React.MutableRefObject<string[]>;
  setHistoryIndex: React.Dispatch<React.SetStateAction<number>>;
};

export const useLoadState = ({
  canvas,
  autoZoom,
  initialState,
  canvasHistory,
  setHistoryIndex,
}: UseLoadStateProps) => {
  const initialized = useRef(false);

  useEffect(() => {
    if (!initialized.current && initialState?.current && canvas) {
      // 处理initialState可能是字符串或对象的情况
      let data;

      if (typeof initialState.current === 'string') {
        console.log('🔄 Parsing canvas data from string');
        try {
          data = JSON.parse(initialState.current);
        } catch (error) {
          console.error('❌ Failed to parse canvas data string:', error);
          console.log('📋 Raw data:', initialState.current);
          return;
        }
      } else if (typeof initialState.current === 'object') {
        console.log('✅ Using canvas data as object');
        data = initialState.current;
      } else {
        console.error('❌ Invalid canvas data type:', typeof initialState.current);
        return;
      }

      console.log('📂 Loading canvas data:', data);

      canvas.loadFromJSON(data, () => {
        const currentState = JSON.stringify(
          canvas.toJSON(JSON_KEYS),
        );

        canvasHistory.current = [currentState];
        setHistoryIndex(0);
        autoZoom();
        console.log('✅ Canvas loaded successfully');
      });
      initialized.current = true;
    }
  },
  [
    canvas,
    autoZoom,
    initialState, // no need, this is a ref
    canvasHistory, // no need, this is a ref
    setHistoryIndex, // no need, this is a dispatch
  ]);
};
