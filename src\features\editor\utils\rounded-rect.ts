import { fabric } from "fabric";

// 扩展Fabric.js的Rect类以支持拖拽圆角
export class RoundedRect extends fabric.Rect {
  private cornerControl: fabric.Circle | null = null;
  private isDraggingCorner = false;

  constructor(options: any = {}) {
    super(options);
    this.setupCornerControl();
  }

  private setupCornerControl() {
    // 重写控制点渲染
    this.on('selected', () => {
      this.addCornerControl();
    });

    this.on('deselected', () => {
      this.removeCornerControl();
    });

    this.on('scaling', () => {
      this.updateCornerControlPosition();
    });

    this.on('moving', () => {
      this.updateCornerControlPosition();
    });
  }

  private addCornerControl() {
    if (this.cornerControl || !this.canvas) return;

    const cornerSize = 8;
    const cornerControl = new fabric.Circle({
      radius: cornerSize,
      fill: '#3b82f6',
      stroke: '#ffffff',
      strokeWidth: 2,
      selectable: false,
      evented: true,
      excludeFromExport: true,
      name: 'cornerControl',
      originX: 'center',
      originY: 'center',
    });

    this.cornerControl = cornerControl;
    this.canvas.add(cornerControl);
    this.updateCornerControlPosition();

    // 添加拖拽事件
    cornerControl.on('mousedown', (e) => {
      this.isDraggingCorner = true;
      this.canvas!.selection = false;
      e.e.stopPropagation();
    });

    this.canvas.on('mouse:move', (e) => {
      if (!this.isDraggingCorner || !this.cornerControl) return;

      const pointer = this.canvas!.getPointer(e.e);
      const rect = this.getBoundingRect();
      
      // 计算圆角半径
      const distanceFromTopLeft = Math.sqrt(
        Math.pow(pointer.x - rect.left, 2) + 
        Math.pow(pointer.y - rect.top, 2)
      );

      // 限制圆角半径在合理范围内
      const maxRadius = Math.min(this.width! / 2, this.height! / 2);
      const newRadius = Math.min(Math.max(0, distanceFromTopLeft - 20), maxRadius);

      this.set({
        rx: newRadius,
        ry: newRadius,
      });

      this.canvas!.renderAll();
      this.updateCornerControlPosition();
    });

    this.canvas.on('mouse:up', () => {
      if (this.isDraggingCorner) {
        this.isDraggingCorner = false;
        this.canvas!.selection = true;
        // 触发保存
        this.canvas!.fire('object:modified', { target: this });
      }
    });
  }

  private removeCornerControl() {
    if (this.cornerControl && this.canvas) {
      this.canvas.remove(this.cornerControl);
      this.cornerControl = null;
    }
  }

  private updateCornerControlPosition() {
    if (!this.cornerControl) return;

    const rect = this.getBoundingRect();
    const rx = this.rx || 0;
    
    // 将圆角控制点放在左上角圆角的边缘
    const controlX = rect.left + rx;
    const controlY = rect.top + rx;

    this.cornerControl.set({
      left: controlX,
      top: controlY,
    });

    this.cornerControl.setCoords();
  }

  // 重写toObject方法以确保圆角属性被保存
  toObject(propertiesToInclude?: string[]) {
    return super.toObject(['rx', 'ry', ...(propertiesToInclude || [])]);
  }

  // 清理方法
  dispose() {
    this.removeCornerControl();
    super.dispose?.();
  }
}

// 注册自定义类到Fabric.js
(fabric as any).RoundedRect = RoundedRect;
