import { useMutation, useQueryClient } from "@tanstack/react-query";
import { projectService, Project, ProjectUpdate } from "@/lib/project-service";

type ResponseType = Project;
type RequestType = ProjectUpdate;

export const useAutoSaveProject = (id: string | number) => {
  const queryClient = useQueryClient();
  const projectId = typeof id === 'string' ? parseInt(id) : id;

  const mutation = useMutation<
    ResponseType,
    Error,
    RequestType
  >({
    mutationKey: ["auto-save-project", { id: projectId }],
    mutationFn: async (projectData) => {
      console.log('💾 Auto-saving project:', projectId);
      console.log('💾 Auto-save data:', {
        canvas_data: projectData.canvas_data ? 'Has canvas data' : 'No canvas data',
        thumbnail_url: projectData.thumbnail_url ? 'Has thumbnail' : 'No thumbnail',
        thumbnail_length: projectData.thumbnail_url?.length || 0,
        width: projectData.width,
        height: projectData.height
      });

      // 如果缩略图太大，先尝试不保存缩略图
      if (projectData.thumbnail_url && projectData.thumbnail_url.length > 200000) {
        console.log('⚠️ Thumbnail too large for auto-save, saving without thumbnail');
        const { thumbnail_url, ...dataWithoutThumbnail } = projectData;
        return await projectService.updateProject(projectId, dataWithoutThumbnail);
      }

      return await projectService.updateProject(projectId, projectData);
    },
    onSuccess: (data) => {
      // 静默更新缓存，不显示通知
      console.log('✅ Auto-save successful');
      console.log('✅ Auto-save response:', {
        id: data.id,
        title: data.title,
        thumbnail_url: data.thumbnail_url ? 'Has thumbnail' : 'No thumbnail',
        thumbnail_length: data.thumbnail_url?.length || 0
      });

      queryClient.setQueryData(["project", { id: projectId }], data);

      // 更新项目列表缓存中的项目信息
      queryClient.setQueryData(["projects"], (oldData: Project[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(project =>
          project.id === projectId ? { ...project, ...data } : project
        );
      });
    },
    onError: (error) => {
      console.error("❌ Auto-save failed:", error);
      console.error("❌ Auto-save error details:", error);

      // 尝试解析错误详情
      if (error instanceof Error) {
        try {
          const errorData = JSON.parse(error.message);
          console.error("❌ Auto-save validation errors:", errorData);
        } catch (e) {
          console.error("❌ Auto-save error message:", error.message);
        }
      }

      // 自动保存失败时也不显示通知，避免打扰用户
      // 可以考虑在状态栏显示一个小的错误指示器
    }
  });

  return mutation;
};
