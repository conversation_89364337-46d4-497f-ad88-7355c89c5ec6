import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { projectService, ProjectCreate, Project } from "@/lib/project-service";
import { checkAuthStatus, forceReLogin } from "@/lib/api-client";

type ResponseType = Project;
type RequestType = ProjectCreate;

export const useCreateProject = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseType, Error, RequestType>({
    mutationFn: async (projectData) => {
      // 在创建项目前检查认证状态
      console.log('🔍 Checking auth status before creating project...');

      // 详细检查localStorage状态
      const accessToken = localStorage.getItem('canva_access_token');
      const refreshToken = localStorage.getItem('canva_refresh_token');
      const tokenExpires = localStorage.getItem('canva_token_expires');

      console.log('🔍 Detailed auth check:', {
        hasAccessToken: !!accessToken,
        accessTokenLength: accessToken?.length || 0,
        accessTokenPreview: accessToken ? `${accessToken.substring(0, 30)}...` : 'null',
        hasRefreshToken: !!refreshToken,
        hasExpires: !!tokenExpires,
        expires: tokenExpires ? new Date(parseInt(tokenExpires)).toISOString() : 'null',
        isExpired: tokenExpires ? Date.now() >= parseInt(tokenExpires) : true,
        allKeys: Object.keys(localStorage)
      });

      if (!checkAuthStatus()) {
        console.error('❌ User not authenticated, forcing re-login');
        forceReLogin();
        throw new Error('用户未登录，请重新登录');
      }

      console.log('✅ Auth status OK, proceeding with project creation');
      console.log('📊 Project data to create:', projectData);

      try {
        const result = await projectService.createProject(projectData);
        console.log('🎉 Project creation successful:', result);
        return result;
      } catch (error) {
        console.error('💥 Project creation failed with error:', error);
        console.error('💥 Error details:', {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          projectData: projectData
        });
        throw error;
      }
    },
    onSuccess: (data) => {
      toast.success("项目创建成功");
      queryClient.invalidateQueries({ queryKey: ["projects"] });

      // 将新创建的项目添加到缓存中
      queryClient.setQueryData(["project", { id: data.id }], data);
    },
    onError: (error) => {
      console.error("Failed to create project:", error);

      // 如果是认证相关错误，强制重新登录
      if (error.message.includes('422') || error.message.includes('401') || error.message.includes('Unauthorized')) {
        console.log('🔐 Authentication error detected, checking login status...');
        if (!checkAuthStatus()) {
          toast.error("登录已过期，请重新登录");
          forceReLogin();
          return;
        }
      }

      toast.error(error.message || "项目创建失败");
    },
  });

  return mutation;
};
