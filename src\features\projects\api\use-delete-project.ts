import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { projectService } from "@/lib/project-service";

export const useDeleteProject = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<void, Error, number>({
    mutationFn: async (id: number) => {
      console.log('🗑️ Deleting project:', id);
      await projectService.deleteProject(id);
    },
    onSuccess: (_, id) => {
      console.log('✅ Project deleted successfully:', id);
      toast.success("项目删除成功");

      // 更新项目列表缓存
      queryClient.invalidateQueries({ queryKey: ["projects"] });
      queryClient.invalidateQueries({ queryKey: ["project", { id }] });
    },
    onError: (error) => {
      console.error('❌ Failed to delete project:', error);
      toast.error("删除项目失败");
    }
  });

  return mutation;
};
