import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { projectService } from "@/lib/project-service";

export const useDeleteProjects = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<void, Error, number[]>({
    mutationFn: async (ids: number[]) => {
      console.log('🗑️ Batch deleting projects:', ids);
      
      // 并行删除所有项目
      await Promise.all(
        ids.map(id => projectService.deleteProject(id))
      );
    },
    onSuccess: (_, ids) => {
      console.log('✅ Projects deleted successfully:', ids);
      toast.success(`成功删除 ${ids.length} 个项目`);
      
      // 更新项目列表缓存
      queryClient.invalidateQueries({ queryKey: ["projects"] });
      
      // 清除单个项目缓存
      ids.forEach(id => {
        queryClient.invalidateQueries({ queryKey: ["project", { id }] });
      });
    },
    onError: (error) => {
      console.error('❌ Failed to delete projects:', error);
      toast.error("批量删除项目失败");
    }
  });

  return mutation;
};
