import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { projectService, Project } from "@/lib/project-service";

export const useDuplicateProject = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<Project, Error, number>({
    mutationFn: async (id: number) => {
      console.log('📋 Duplicating project:', id);

      // 获取原项目数据
      const originalProject = await projectService.getProject(id);

      // 创建副本
      const duplicateData = {
        title: `${originalProject.title} - 副本`,
        description: originalProject.description,
        canvas_data: originalProject.canvas_data,
        thumbnail_url: originalProject.thumbnail_url,
        width: originalProject.width,
        height: originalProject.height,
        is_public: false, // 副本默认为私有
        is_template: false
      };

      return await projectService.createProject(duplicateData);
    },
    onSuccess: (newProject) => {
      console.log('✅ Project duplicated successfully:', newProject.id);
      toast.success(`项目已复制为 "${newProject.title}"`);

      // 更新项目列表缓存
      queryClient.invalidateQueries({ queryKey: ["projects"] });
    },
    onError: (error) => {
      console.error('❌ Failed to duplicate project:', error);
      toast.error("复制项目失败");
    }
  });

  return mutation;
};
