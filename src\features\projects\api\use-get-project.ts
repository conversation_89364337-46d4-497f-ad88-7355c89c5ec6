import { useQuery } from "@tanstack/react-query";
import { projectService, Project } from "@/lib/project-service";

export type ResponseType = Project;

export const useGetProject = (id: string | number) => {
  const projectId = typeof id === 'string' ? parseInt(id) : id;

  const query = useQuery<ResponseType, Error>({
    enabled: !!projectId && !isNaN(projectId),
    queryKey: ["project", { id: projectId }],
    queryFn: () => projectService.getProject(projectId),
    staleTime: 2 * 60 * 1000, // 2分钟
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return query;
};
