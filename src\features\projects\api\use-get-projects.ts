import { useQuery } from "@tanstack/react-query";
import { projectService, Project } from "@/lib/project-service";

export type ResponseType = Project[];

export const useGetProjects = (params?: {
  status?: 'draft' | 'published' | 'archived';
  skip?: number;
  limit?: number;
}) => {
  const query = useQuery<ResponseType, Error>({
    queryKey: ["projects", params],
    queryFn: async () => {
      console.log('🔍 useGetProjects: Starting to fetch projects with params:', params);

      try {
        const projects = await projectService.getProjects(params);
        console.log('✅ useGetProjects: Successfully fetched projects:', {
          count: projects.length,
          projects: projects.map(p => ({
            id: p.id,
            title: p.title,
            updated_at: p.updated_at,
            thumbnail_url: p.thumbnail_url ? 'Has thumbnail' : 'No thumbnail',
            thumbnail_length: p.thumbnail_url?.length || 0
          }))
        });
        return projects;
      } catch (error) {
        console.error('❌ useGetProjects: Failed to fetch projects:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5分钟
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  console.log('🔍 useGetProjects query state:', {
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error?.message,
    dataLength: query.data?.length,
    status: query.status
  });

  return query;
};
