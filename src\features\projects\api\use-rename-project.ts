import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { projectService, Project } from "@/lib/project-service";

interface RenameProjectData {
  id: number;
  title: string;
}

export const useRenameProject = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<Project, Error, RenameProjectData>({
    mutationFn: async ({ id, title }: RenameProjectData) => {
      console.log('✏️ Renaming project:', id, 'to:', title);
      
      return await projectService.updateProject(id, { title });
    },
    onSuccess: (updatedProject) => {
      console.log('✅ Project renamed successfully:', updatedProject.title);
      toast.success(`项目已重命名为 "${updatedProject.title}"`);
      
      // 更新项目列表缓存
      queryClient.invalidateQueries({ queryKey: ["projects"] });
      queryClient.setQueryData(["project", { id: updatedProject.id }], updatedProject);
    },
    onError: (error) => {
      console.error('❌ Failed to rename project:', error);
      toast.error("重命名项目失败");
    }
  });

  return mutation;
};
