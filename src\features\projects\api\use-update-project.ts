import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { projectService, Project, ProjectUpdate } from "@/lib/project-service";

type ResponseType = Project;
type RequestType = ProjectUpdate;

export const useUpdateProject = (id: string | number) => {
  const queryClient = useQueryClient();
  const projectId = typeof id === 'string' ? parseInt(id) : id;

  const mutation = useMutation<
    ResponseType,
    Error,
    RequestType
  >({
    mutationKey: ["project", { id: projectId }],
    mutationFn: async (projectData) => {
      return await projectService.updateProject(projectId, projectData);
    },
    onSuccess: (data) => {
      toast.success("项目更新成功");
      queryClient.invalidateQueries({ queryKey: ["projects"] });
      queryClient.setQueryData(["project", { id: projectId }], data);
    },
    onError: (error) => {
      console.error("Failed to update project:", error);
      toast.error(error.message || "项目更新失败");
    }
  });

  return mutation;
};
