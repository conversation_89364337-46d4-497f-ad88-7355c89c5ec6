/**
 * 认证相关的React Hook
 */
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { authService, User, LoginCredentials, RegisterData } from '@/lib/auth-service';

export interface UseAuthReturn {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  error: string | null;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // 检查认证状态并获取用户信息
  const checkAuth = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!authService.isAuthenticated()) {
        setUser(null);
        return;
      }

      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
      setError('认证检查失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // 登录
  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      await authService.login(credentials);
      await checkAuth(); // 登录成功后获取用户信息
      
      return true;
    } catch (error: any) {
      console.error('Login failed:', error);
      setError(error.message || '登录失败');
      return false;
    } finally {
      setLoading(false);
    }
  }, [checkAuth]);

  // 注册
  const register = useCallback(async (userData: RegisterData): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      await authService.register(userData);
      
      return true;
    } catch (error: any) {
      console.error('Registration failed:', error);
      setError(error.message || '注册失败');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // 登出
  const logout = useCallback(async () => {
    try {
      setLoading(true);
      await authService.logout();
      setUser(null);
      router.push('/sign-in');
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setLoading(false);
    }
  }, [router]);

  // 刷新用户信息
  const refreshUser = useCallback(async () => {
    await checkAuth();
  }, [checkAuth]);

  return {
    user,
    loading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    refreshUser,
    error,
  };
}
