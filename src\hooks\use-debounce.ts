/**
 * 防抖Hook - 用于自动保存等功能
 */
import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * 防抖Hook
 * @param value 需要防抖的值
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的值
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * 防抖回调Hook
 * @param callback 需要防抖的回调函数
 * @param delay 延迟时间（毫秒）
 * @param deps 依赖数组
 * @returns 防抖后的回调函数
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay, ...deps]
  ) as T;

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

/**
 * 自动保存Hook
 * @param saveFunction 保存函数
 * @param data 需要保存的数据
 * @param delay 防抖延迟时间（毫秒）
 * @param enabled 是否启用自动保存
 */
export function useAutoSaveDebounced<T>(
  saveFunction: (data: T) => Promise<void>,
  data: T,
  delay: number = 2000,
  enabled: boolean = true
) {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout>();
  const lastDataRef = useRef<T>(data);

  const debouncedSave = useCallback(async (dataToSave: T) => {
    if (!enabled) return;

    try {
      setIsSaving(true);
      setError(null);

      await saveFunction(dataToSave);
      setLastSaved(new Date());
      
      console.log('✅ Auto-save successful at', new Date().toLocaleTimeString());
    } catch (error: any) {
      console.error('❌ Auto-save failed:', error);
      setError(error.message || '自动保存失败');
    } finally {
      setIsSaving(false);
    }
  }, [saveFunction, enabled]);

  useEffect(() => {
    // 检查数据是否真的发生了变化
    if (!enabled || JSON.stringify(data) === JSON.stringify(lastDataRef.current)) {
      return;
    }

    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 设置新的定时器
    saveTimeoutRef.current = setTimeout(() => {
      debouncedSave(data);
      lastDataRef.current = data;
    }, delay);

    // 清理函数
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [data, delay, enabled, debouncedSave]);

  // 手动保存函数
  const saveNow = useCallback(async () => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }
    await debouncedSave(data);
    lastDataRef.current = data;
  }, [data, debouncedSave]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  return {
    isSaving,
    lastSaved,
    error,
    saveNow,
  };
}
