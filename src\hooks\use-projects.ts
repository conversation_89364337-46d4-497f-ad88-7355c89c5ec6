/**
 * 项目管理相关的React Hook
 */
import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { projectService, Project, ProjectCreate, ProjectUpdate } from '@/lib/project-service';

// 获取项目列表Hook
export function useGetProjects(params?: {
  status?: 'draft' | 'published' | 'archived';
  skip?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: ['projects', params],
    queryFn: () => projectService.getProjects(params),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

// 获取单个项目Hook
export function useGetProject(id: number) {
  return useQuery({
    queryKey: ['project', id],
    queryFn: () => projectService.getProject(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

// 创建项目Hook
export function useCreateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (projectData: ProjectCreate) => projectService.createProject(projectData),
    onSuccess: () => {
      // 刷新项目列表
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

// 更新项目Hook
export function useUpdateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: ProjectUpdate }) => 
      projectService.updateProject(id, data),
    onSuccess: (data) => {
      // 更新缓存
      queryClient.setQueryData(['project', data.id], data);
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

// 删除项目Hook
export function useDeleteProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => projectService.deleteProject(id),
    onSuccess: () => {
      // 刷新项目列表
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

// 复制项目Hook
export function useDuplicateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, title }: { id: number; title?: string }) => 
      projectService.duplicateProject(id, title),
    onSuccess: () => {
      // 刷新项目列表
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
  });
}

// 自动保存Hook
export function useAutoSave(projectId: number) {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  const saveCanvas = useCallback(async (canvasData: any) => {
    if (!projectId || !canvasData) return;

    try {
      setIsSaving(true);
      setError(null);

      await projectService.saveProjectCanvas(projectId, canvasData);
      setLastSaved(new Date());
      
      console.log('✅ Auto-save successful');
    } catch (error: any) {
      console.error('❌ Auto-save failed:', error);
      setError(error.message || '自动保存失败');
    } finally {
      setIsSaving(false);
    }
  }, [projectId]);

  return {
    saveCanvas,
    isSaving,
    lastSaved,
    error,
  };
}

// 项目统计Hook
export function useProjectStats() {
  return useQuery({
    queryKey: ['project-stats'],
    queryFn: () => projectService.getProjectStats(),
    staleTime: 10 * 60 * 1000, // 10分钟
  });
}

// 搜索项目Hook
export function useSearchProjects() {
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<Project[]>([]);
  const [error, setError] = useState<string | null>(null);

  const searchProjects = useCallback(async (query: string, isPublicOnly = false) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      setError(null);

      const results = await projectService.searchProjects({
        query: query.trim(),
        is_public_only: isPublicOnly,
        limit: 20,
      });

      setSearchResults(results);
    } catch (error: any) {
      console.error('❌ Search failed:', error);
      setError(error.message || '搜索失败');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  const clearSearch = useCallback(() => {
    setSearchResults([]);
    setError(null);
  }, []);

  return {
    searchProjects,
    clearSearch,
    isSearching,
    searchResults,
    error,
  };
}
