/**
 * FastAPI后端API客户端 - 使用原生fetch
 */

// API基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const API_TIMEOUT = 10000; // 10秒超时

// 获取认证token
function getAuthToken(): string | null {
  if (typeof window === 'undefined') {
    console.log('🔍 Server-side environment, no token available');
    return null;
  }

  // 尝试从localStorage获取token
  const token = localStorage.getItem('canva_access_token');

  if (!token) {
    console.warn('⚠️ No auth token found in localStorage');
    console.log('🔍 Available localStorage keys:', Object.keys(localStorage));

    // 检查是否有其他相关的存储项
    const refreshToken = localStorage.getItem('canva_refresh_token');
    const tokenExpires = localStorage.getItem('canva_token_expires');

    console.log('🔍 Auth storage status:', {
      hasAccessToken: !!token,
      hasRefreshToken: !!refreshToken,
      hasExpires: !!tokenExpires,
      expires: tokenExpires
    });
  }

  return token;
}

// 清除认证token
function clearAuthTokens(): void {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('canva_access_token');
  localStorage.removeItem('canva_refresh_token');
  localStorage.removeItem('canva_token_expires');
}

// 创建带超时的fetch
async function fetchWithTimeout(url: string, options: RequestInit = {}): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// API客户端类
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = `${baseURL}/api/v1`;
  }

  // 创建请求头
  private createHeaders(customHeaders: Record<string, string> = {}): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...customHeaders,
    };

    const token = getAuthToken();
    console.log('🔑 Auth token check:', {
      hasToken: !!token,
      tokenLength: token?.length || 0,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'null'
    });

    if (token) {
      // 后端期望的是 'token' header，而不是 'Authorization: Bearer'
      headers.token = token;
      console.log('🔐 Token header added');
    } else {
      console.log('⚠️ No auth token found in localStorage');
    }

    return headers;
  }

  // 处理响应
  private async handleResponse<T>(response: Response): Promise<T> {
    console.log('✅ API Response:', {
      status: response.status,
      url: response.url,
      ok: response.ok
    });

    // 处理401未授权错误
    if (response.status === 401) {
      console.log('🔐 Unauthorized, clearing tokens...');
      clearAuthTokens();
      if (typeof window !== 'undefined') {
        window.location.href = '/sign-in';
      }
      throw new Error('Unauthorized');
    }

    // 处理其他HTTP错误
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      let errorDetails = null;

      try {
        const errorData = await response.json();
        errorDetails = errorData;
        errorMessage = errorData.message || errorData.msg || errorData.detail || errorMessage;

        console.error('❌ API Error Details:', {
          status: response.status,
          url: response.url,
          errorData: errorData
        });
      } catch (parseError) {
        console.error('❌ Failed to parse error response:', parseError);
      }

      throw new Error(errorMessage);
    }

    // 处理空响应
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.log('⚠️ Non-JSON response, returning empty object');
      return {} as T;
    }

    try {
      const jsonData = await response.json();
      console.log('📦 Parsed JSON response:', jsonData);
      return jsonData;
    } catch (error) {
      console.error('❌ Failed to parse JSON response:', error);
      throw new Error('Invalid JSON response');
    }
  }

  // GET请求
  async get<T = any>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const urlObj = new URL(url);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          urlObj.searchParams.append(key, String(value));
        }
      });
    }

    console.log('🔄 GET Request:', urlObj.toString());

    const response = await fetchWithTimeout(urlObj.toString(), {
      method: 'GET',
      headers: this.createHeaders(),
    });

    return this.handleResponse<T>(response);
  }

  // POST请求
  async post<T = any>(endpoint: string, data?: any): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    console.log('🔄 POST Request:', url, data);

    const response = await fetchWithTimeout(url, {
      method: 'POST',
      headers: this.createHeaders(),
      body: data ? JSON.stringify(data) : undefined,
    });

    return this.handleResponse<T>(response);
  }

  // PUT请求
  async put<T = any>(endpoint: string, data?: any): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    console.log('🔄 PUT Request:', url, data);

    const response = await fetchWithTimeout(url, {
      method: 'PUT',
      headers: this.createHeaders(),
      body: data ? JSON.stringify(data) : undefined,
    });

    return this.handleResponse<T>(response);
  }

  // DELETE请求
  async delete<T = any>(endpoint: string): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    console.log('🔄 DELETE Request:', url);

    const response = await fetchWithTimeout(url, {
      method: 'DELETE',
      headers: this.createHeaders(),
    });

    return this.handleResponse<T>(response);
  }

  // PATCH请求
  async patch<T = any>(endpoint: string, data?: any): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    console.log('🔄 PATCH Request:', url, data);

    const response = await fetchWithTimeout(url, {
      method: 'PATCH',
      headers: this.createHeaders(),
      body: data ? JSON.stringify(data) : undefined,
    });

    return this.handleResponse<T>(response);
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient(API_BASE_URL);

// API响应类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    size: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// 通用API方法 - 简化版本，直接使用apiClient
export const api = {
  // GET请求
  async get<T = any>(endpoint: string, params?: any): Promise<T> {
    return apiClient.get<T>(endpoint, params);
  },

  // POST请求
  async post<T = any>(endpoint: string, data?: any): Promise<T> {
    return apiClient.post<T>(endpoint, data);
  },

  // PUT请求
  async put<T = any>(endpoint: string, data?: any): Promise<T> {
    return apiClient.put<T>(endpoint, data);
  },

  // DELETE请求
  async delete<T = any>(endpoint: string): Promise<T> {
    return apiClient.delete<T>(endpoint);
  },

  // PATCH请求
  async patch<T = any>(endpoint: string, data?: any): Promise<T> {
    return apiClient.patch<T>(endpoint, data);
  }
};

// 错误处理工具
export const handleApiError = (error: any): string => {
  if (error.message) {
    return error.message;
  }
  return '发生未知错误';
};

// 检查API连接状态
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    await apiClient.get('/health');
    return true;
  } catch (error) {
    console.error('❌ API Health Check Failed:', error);
    return false;
  }
};

// 检查用户是否已登录
export const checkAuthStatus = (): boolean => {
  if (typeof window === 'undefined') return false;

  const token = localStorage.getItem('canva_access_token');
  const expires = localStorage.getItem('canva_token_expires');

  if (!token) {
    console.log('🔐 No access token found');
    return false;
  }

  if (expires) {
    const expiresTime = parseInt(expires);
    const now = Date.now();

    if (now >= expiresTime) {
      console.log('🔐 Token expired');
      return false;
    }
  }

  console.log('✅ User is authenticated');
  return true;
};

// 强制重新登录
export const forceReLogin = (): void => {
  console.log('🔐 Forcing re-login...');
  if (typeof window !== 'undefined') {
    localStorage.removeItem('canva_access_token');
    localStorage.removeItem('canva_refresh_token');
    localStorage.removeItem('canva_token_expires');
    window.location.href = '/sign-in';
  }
};

export default apiClient;
