/**
 * FastAPI后端认证服务
 */
import { api, handleApiError } from './api-client';

// 认证相关类型定义
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  bio?: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  nickname?: string;
  avatar_url?: string;
  bio?: string;
  is_active: boolean;
  is_verified: boolean;
  is_premium: boolean;
  is_superuser: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  username: string;
}

export interface LoginResponse {
  code: number;
  msg: string;
  data: TokenResponse;
}

export interface PasswordChangeData {
  old_password: string;
  new_password: string;
}

// 认证服务类
export class AuthService {
  private static instance: AuthService;

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<TokenResponse> {
    try {
      console.log('🔐 Attempting login for:', credentials.username);

      const response = await api.post<LoginResponse>('/base/access_token', credentials);
      console.log('📥 Login response received:', response);

      // 检查响应格式
      if (!response || typeof response !== 'object') {
        console.error('❌ Invalid response format:', response);
        throw new Error('服务器响应格式错误');
      }

      if (response.code !== 200) {
        console.error('❌ Login failed - server error:', response.msg);
        throw new Error(response.msg || '登录失败');
      }

      if (!response.data) {
        console.error('❌ Login failed - no data in response:', response);
        throw new Error('服务器未返回登录数据');
      }

      const tokenData = response.data;
      console.log('🎫 Token data received:', {
        hasAccessToken: !!tokenData.access_token,
        hasRefreshToken: !!tokenData.refresh_token,
        username: tokenData.username
      });

      // 存储token到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('canva_access_token', tokenData.access_token);
        localStorage.setItem('canva_refresh_token', tokenData.refresh_token);
        // 设置默认过期时间为24小时
        localStorage.setItem('canva_token_expires',
          (Date.now() + 24 * 60 * 60 * 1000).toString()
        );
        console.log('💾 Tokens stored in localStorage');
      }

      console.log('✅ Login successful');
      return tokenData;
    } catch (error) {
      console.error('❌ Login failed:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 用户注册
   */
  async register(userData: RegisterData): Promise<{ message: string; user_id: number }> {
    try {
      console.log('📝 Attempting registration for:', userData.username);

      const response = await api.post<{ code: number; msg: string; data: { message: string; user_id: number } }>('/base/register', userData);

      if (response.code !== 200 || !response.data) {
        throw new Error(response.msg || '注册失败');
      }

      console.log('✅ Registration successful');
      return response.data;
    } catch (error) {
      console.error('❌ Registration failed:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await api.get<{ code: number; msg: string; data: User }>('/base/userinfo');

      if (response.code !== 200 || !response.data) {
        throw new Error(response.msg || '获取用户信息失败');
      }

      return response.data;
    } catch (error) {
      console.error('❌ Failed to get current user:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('⚠️ Logout API call failed:', error);
      // 即使API调用失败，也要清除本地token
    } finally {
      // 清除本地存储的token
      if (typeof window !== 'undefined') {
        localStorage.removeItem('canva_access_token');
        localStorage.removeItem('canva_refresh_token');
        localStorage.removeItem('canva_token_expires');
      }
      console.log('✅ Logout completed');
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(): Promise<TokenResponse> {
    try {
      const refreshToken = typeof window !== 'undefined' 
        ? localStorage.getItem('canva_refresh_token') 
        : null;

      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await api.post<TokenResponse>('/auth/refresh', {}, {
        headers: { Authorization: `Bearer ${refreshToken}` }
      });

      // 更新存储的token
      if (typeof window !== 'undefined') {
        localStorage.setItem('canva_access_token', response.access_token);
        localStorage.setItem('canva_token_expires', 
          (Date.now() + response.expires_in * 1000).toString()
        );
      }

      console.log('✅ Token refreshed successfully');
      return response;
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      // 刷新失败，清除所有token
      this.logout();
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 修改密码
   */
  async changePassword(passwordData: PasswordChangeData): Promise<{ message: string }> {
    try {
      const response = await api.post('/auth/change-password', passwordData);
      console.log('✅ Password changed successfully');
      return response;
    } catch (error) {
      console.error('❌ Password change failed:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false;
    
    const token = localStorage.getItem('canva_access_token');
    const expires = localStorage.getItem('canva_token_expires');
    
    if (!token || !expires) return false;
    
    // 检查token是否过期
    const expiresTime = parseInt(expires);
    const now = Date.now();
    
    if (now >= expiresTime) {
      console.log('🔐 Token expired, attempting refresh...');
      // Token过期，尝试刷新
      this.refreshToken().catch(() => {
        console.log('❌ Token refresh failed, user needs to login again');
      });
      return false;
    }
    
    return true;
  }

  /**
   * 获取存储的访问令牌
   */
  getAccessToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('canva_access_token');
  }
}

// 导出单例实例
export const authService = AuthService.getInstance();

// 默认导出
export default authService;
