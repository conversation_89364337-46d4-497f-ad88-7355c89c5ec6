/**
 * JWT工具函数
 */

export interface JWTPayload {
  user_id: number;
  username: string;
  is_superuser: boolean;
  exp: number;
  aud: string;
  iss: string;
  iat: number;
}

/**
 * 解析JWT token (仅解析payload，不验证签名)
 * 注意：这只用于客户端读取token信息，不用于安全验证
 */
export function parseJWTPayload(token: string): JWTPayload | null {
  try {
    // JWT格式: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid JWT format');
      return null;
    }

    // 解码payload (base64url)
    const payload = parts[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    const parsed = JSON.parse(decoded);

    console.log('🔍 Parsed JWT payload:', parsed);
    return parsed;
  } catch (error) {
    console.error('❌ Failed to parse JWT:', error);
    return null;
  }
}

/**
 * 检查JWT是否过期
 */
export function isJWTExpired(token: string): boolean {
  const payload = parseJWTPayload(token);
  if (!payload) return true;

  const now = Math.floor(Date.now() / 1000);
  return payload.exp < now;
}

/**
 * 从JWT token中提取用户信息
 */
export function extractUserFromJWT(token: string) {
  const payload = parseJWTPayload(token);
  if (!payload) return null;

  return {
    id: payload.user_id.toString(),
    username: payload.username,
    email: `${payload.username}@example.com`, // 临时邮箱，实际应该从用户信息API获取
    name: payload.username,
    is_superuser: payload.is_superuser,
  };
}
