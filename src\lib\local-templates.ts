// 本地模板数据
export interface LocalTemplate {
  id: string;
  name: string;
  width: number;
  height: number;
  isPro: boolean;
  thumbnailUrl?: string;
  json: string;
}

// 读取现有模板JSON文件的辅助函数
const loadTemplateJson = async (filename: string) => {
  try {
    const response = await fetch(`/${filename}`);
    return await response.text();
  } catch (error) {
    console.error(`Failed to load template ${filename}:`, error);
    return '{"objects":[]}';
  }
};

export const localTemplates: LocalTemplate[] = [
  {
    id: "template-car-sale",
    name: "Car Sale",
    width: 1080,
    height: 1080,
    isPro: false,
    thumbnailUrl: "/car_sale.png",
    json: '{"objects":[]}'  // 将在运行时加载
  },
  {
    id: "template-coming-soon",
    name: "Coming Soon",
    width: 1080,
    height: 1080,
    isPro: false,
    thumbnailUrl: "/coming_soon.png",
    json: '{"objects":[]}'  // 将在运行时加载
  },
  {
    id: "template-flash-sale",
    name: "Flash Sale",
    width: 1080,
    height: 1080,
    isPro: true,
    thumbnailUrl: "/flash_sale.png",
    json: '{"objects":[]}'  // 将在运行时加载
  },
  {
    id: "template-travel",
    name: "Travel",
    width: 1080,
    height: 1080,
    isPro: false,
    thumbnailUrl: "/travel.png",
    json: '{"objects":[]}'  // 将在运行时加载
  },
  {
    id: "template-phone-wallpaper",
    name: "Phone Wallpaper",
    width: 1080,
    height: 1920,
    isPro: false,
    thumbnailUrl: "/phone_wallpaper.png",
    json: '{"objects":[]}'  // 将在运行时加载
  }
];

// 生成项目ID的辅助函数
export const generateProjectId = (): string => {
  return crypto.randomUUID();
};

// 加载模板JSON数据
export const loadTemplateData = async (templateId: string): Promise<string> => {
  const template = localTemplates.find(t => t.id === templateId);
  if (!template) {
    return '{"objects":[]}';
  }

  // 根据模板ID加载对应的JSON文件
  const jsonFiles: Record<string, string> = {
    "template-car-sale": "car_sale.json",
    "template-coming-soon": "coming_soon.json",
    "template-flash-sale": "flash_sale.json",
    "template-travel": "travel.json",
    "template-phone-wallpaper": "phone_wallpaper.json"
  };

  const filename = jsonFiles[templateId];
  if (filename) {
    try {
      const response = await fetch(`/${filename}`);
      if (response.ok) {
        return await response.text();
      }
    } catch (error) {
      console.error(`Failed to load template ${filename}:`, error);
    }
  }

  return '{"objects":[]}';
};

// 创建项目的本地函数
export const createLocalProject = async (template?: LocalTemplate) => {
  const projectId = generateProjectId();

  if (template) {
    // 加载模板的实际JSON数据
    const templateJson = await loadTemplateData(template.id);

    return {
      id: projectId,
      name: `${template.name} project`,
      json: templateJson,
      width: template.width,
      height: template.height,
      isTemplate: false,
      isPro: false,
      thumbnailUrl: null,
      userId: "local-user",
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // 默认空白项目
  return {
    id: projectId,
    name: "Untitled project",
    json: JSON.stringify({
      version: "5.3.0",
      objects: []
    }),
    width: 900,
    height: 1200,
    isTemplate: false,
    isPro: false,
    thumbnailUrl: null,
    userId: "local-user",
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};
