/**
 * FastAPI后端项目管理服务
 */
import { api, handleApiError, PaginatedResponse } from './api-client';

// 项目相关类型定义
export interface Project {
  id: number;
  title: string;
  description?: string;
  canvas_data: any; // Fabric.js画布数据
  thumbnail_url?: string;
  width: number;
  height: number;
  status: 'draft' | 'published' | 'archived';
  is_public: boolean;
  is_template: boolean;
  user_id: number;
  view_count: number;
  like_count: number;
  created_at: string;
  updated_at: string;
}

export interface ProjectCreate {
  title: string;
  description?: string;
  canvas_data?: any;
  width?: number;
  height?: number;
  is_public?: boolean;
  is_template?: boolean;
}

export interface ProjectUpdate {
  title?: string;
  description?: string;
  canvas_data?: any;
  thumbnail_url?: string | null;
  width?: number;
  height?: number;
  status?: 'draft' | 'published' | 'archived';
  is_public?: boolean;
  is_template?: boolean;
}

export interface ProjectSaveRequest {
  canvas_data: any;
}

export interface ProjectDuplicateRequest {
  title?: string;
}

export interface ProjectSearchRequest {
  query: string;
  is_public_only?: boolean;
  skip?: number;
  limit?: number;
}

export interface ProjectStats {
  total_projects: number;
  draft_projects: number;
  published_projects: number;
  archived_projects: number;
  total_views: number;
  total_likes: number;
}

// 项目服务类
export class ProjectService {
  private static instance: ProjectService;

  public static getInstance(): ProjectService {
    if (!ProjectService.instance) {
      ProjectService.instance = new ProjectService();
    }
    return ProjectService.instance;
  }

  /**
   * 获取项目列表
   */
  async getProjects(params?: {
    status?: 'draft' | 'published' | 'archived';
    skip?: number;
    limit?: number;
  }): Promise<Project[]> {
    try {
      console.log('📋 Fetching projects with params:', params);
      
      const response = await api.get<Project[]>('/canva/projects/', params);
      
      console.log(`✅ Fetched ${response.length} projects`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch projects:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 获取公开项目列表
   */
  async getPublicProjects(params?: {
    skip?: number;
    limit?: number;
  }): Promise<Project[]> {
    try {
      console.log('🌐 Fetching public projects');
      
      const response = await api.get<Project[]>('/canva/projects/public', params);
      
      console.log(`✅ Fetched ${response.length} public projects`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch public projects:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 获取项目详情
   */
  async getProject(id: number): Promise<Project> {
    try {
      console.log('📄 Fetching project:', id);
      
      const response = await api.get<Project>(`/canva/projects/${id}`);
      
      console.log('✅ Project fetched successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch project:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 创建新项目
   */
  async createProject(projectData: ProjectCreate): Promise<Project> {
    try {
      console.log('🆕 Creating new project:', projectData.title);
      console.log('📋 Full project data:', JSON.stringify(projectData, null, 2));

      // 确保canvas_data是对象而不是字符串
      const cleanedData = { ...projectData };
      if (cleanedData.canvas_data && typeof cleanedData.canvas_data === 'string') {
        console.log('⚠️ canvas_data is string, parsing to object');
        try {
          cleanedData.canvas_data = JSON.parse(cleanedData.canvas_data);
        } catch (parseError) {
          console.error('❌ Failed to parse canvas_data string:', parseError);
          throw new Error('Invalid canvas_data format');
        }
      }

      console.log('🔧 Cleaned project data:', JSON.stringify(cleanedData, null, 2));

      const response = await api.post<Project>('/canva/projects/', cleanedData);

      console.log('✅ Project created successfully:', response.id);
      return response;
    } catch (error) {
      console.error('❌ Failed to create project:', error);
      console.error('❌ Project data that failed:', JSON.stringify(projectData, null, 2));
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 更新项目
   */
  async updateProject(id: number, projectData: ProjectUpdate): Promise<Project> {
    try {
      console.log('📝 Updating project:', id);
      console.log('📝 Update data:', {
        canvas_data: projectData.canvas_data ? 'Has canvas data' : 'No canvas data',
        thumbnail_url: projectData.thumbnail_url ? 'Has thumbnail' : 'No thumbnail',
        thumbnail_length: projectData.thumbnail_url?.length || 0,
        width: projectData.width,
        height: projectData.height
      });

      // 确保canvas_data是对象而不是字符串
      const cleanedData = { ...projectData };
      if (cleanedData.canvas_data && typeof cleanedData.canvas_data === 'string') {
        console.log('⚠️ canvas_data is string, parsing to object');
        try {
          cleanedData.canvas_data = JSON.parse(cleanedData.canvas_data);
        } catch (parseError) {
          console.error('❌ Failed to parse canvas_data string:', parseError);
          throw new Error('Invalid canvas_data format');
        }
      }

      console.log('📝 Sending cleaned data to API');
      const response = await api.put<Project>(`/canva/projects/${id}`, cleanedData);

      console.log('✅ Project updated successfully, response thumbnail:',
        response.thumbnail_url ? 'Has thumbnail' : 'No thumbnail');
      return response;
    } catch (error) {
      console.error('❌ Failed to update project:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 删除项目
   */
  async deleteProject(id: number): Promise<void> {
    try {
      console.log('🗑️ Deleting project:', id);
      
      await api.delete(`/canva/projects/${id}`);
      
      console.log('✅ Project deleted successfully');
    } catch (error) {
      console.error('❌ Failed to delete project:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 保存项目画布数据（自动保存功能）
   */
  async saveProjectCanvas(id: number, canvasData: any): Promise<Project> {
    try {
      console.log('💾 Saving project canvas:', id);

      // 确保canvasData是对象而不是字符串
      let cleanedCanvasData = canvasData;
      if (typeof canvasData === 'string') {
        console.log('⚠️ canvasData is string, parsing to object');
        try {
          cleanedCanvasData = JSON.parse(canvasData);
        } catch (parseError) {
          console.error('❌ Failed to parse canvasData string:', parseError);
          throw new Error('Invalid canvas data format');
        }
      }

      const response = await api.post<Project>(`/canva/projects/${id}/save`, {
        canvas_data: cleanedCanvasData
      });

      console.log('✅ Canvas saved successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to save canvas:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 复制项目
   */
  async duplicateProject(id: number, newTitle?: string): Promise<Project> {
    try {
      console.log('📋 Duplicating project:', id);
      
      const response = await api.post<Project>(`/canva/projects/${id}/duplicate`, {
        title: newTitle
      });
      
      console.log('✅ Project duplicated successfully:', response.id);
      return response;
    } catch (error) {
      console.error('❌ Failed to duplicate project:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 发布项目
   */
  async publishProject(id: number): Promise<Project> {
    try {
      console.log('📢 Publishing project:', id);
      
      const response = await api.post<Project>(`/canva/projects/${id}/publish`);
      
      console.log('✅ Project published successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to publish project:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 归档项目
   */
  async archiveProject(id: number): Promise<Project> {
    try {
      console.log('📦 Archiving project:', id);
      
      const response = await api.post<Project>(`/canva/projects/${id}/archive`);
      
      console.log('✅ Project archived successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to archive project:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 搜索项目
   */
  async searchProjects(searchData: ProjectSearchRequest): Promise<Project[]> {
    try {
      console.log('🔍 Searching projects:', searchData.query);
      
      const response = await api.post<Project[]>('/canva/projects/search', searchData);
      
      console.log(`✅ Found ${response.length} projects`);
      return response;
    } catch (error) {
      console.error('❌ Failed to search projects:', error);
      throw new Error(handleApiError(error));
    }
  }

  /**
   * 获取项目统计信息
   */
  async getProjectStats(): Promise<ProjectStats> {
    try {
      console.log('📊 Fetching project stats');
      
      const response = await api.get<ProjectStats>('/canva/projects/stats/overview');
      
      console.log('✅ Project stats fetched successfully');
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch project stats:', error);
      throw new Error(handleApiError(error));
    }
  }
}

// 导出单例实例
export const projectService = ProjectService.getInstance();

// 默认导出
export default projectService;
