const { spawn } = require('child_process');

console.log('🔨 Testing Next.js build...');

const buildProcess = spawn('npx', ['next', 'build'], {
  stdio: 'inherit',
  shell: true
});

buildProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Build successful!');
  } else {
    console.error(`❌ Build failed with exit code ${code}`);
    process.exit(1);
  }
});

buildProcess.on('error', (error) => {
  console.error('❌ Build process error:', error);
  process.exit(1);
});
